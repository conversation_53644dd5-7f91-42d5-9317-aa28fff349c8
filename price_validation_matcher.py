import pandas as pd
import numpy as np

def load_4xlsx():
    """4.xlsx 파일을 로드합니다."""
    print("=== 4.xlsx 파일 로드 ===")
    
    # 첫 번째 헤더 (행 1)로 로드
    df = pd.read_excel("4.xlsx", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['QAID', '4차', '5차', '상품단가구간', '수리결과']
    df_selected = df[required_columns].copy()
    
    # NaN 값 제거
    df_selected = df_selected.dropna().reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"컬럼: {list(df_selected.columns)}")
    print("데이터 샘플:")
    print(df_selected.head())
    
    # 데이터 통계
    print(f"\n=== 4.xlsx 데이터 통계 ===")
    print(f"4차 카테고리: {df_selected['4차'].nunique()}개")
    print(f"5차 카테고리: {df_selected['5차'].nunique()}개")
    print(f"상품단가구간: {df_selected['상품단가구간'].unique()}")
    print(f"수리결과: {df_selected['수리결과'].unique()}")
    
    return df_selected

def load_price_new():
    """price_new.xlsx 파일을 로드합니다."""
    print("\n=== price_new.xlsx 파일 로드 ===")
    
    # 데이터유효성_일반 시트의 두 번째 헤더 (행 2)로 로드
    df = pd.read_excel("price_new.xlsx", sheet_name="데이터유효성_일반", header=2)
    
    # 필요한 컬럼만 선택
    required_columns = ['4차', '5차', '상품단가구간', '수리_부품교체', '수리_세척', '수리_SW', '수리_기타', '검수']
    df_selected = df[required_columns].copy()
    
    # NaN 값이 있는 행 제거
    df_selected = df_selected.dropna(subset=['4차', '5차', '상품단가구간']).reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"컬럼: {list(df_selected.columns)}")
    print("데이터 샘플:")
    print(df_selected.head())
    
    return df_selected

def create_matching_key(row):
    """매칭을 위한 키를 생성합니다."""
    return f"{row['4차']}|{row['5차']}|{row['상품단가구간']}"

def perform_matching_and_validation(df_4xlsx, df_price):
    """매칭 및 유효성 검증을 수행합니다."""
    print("\n=== 매칭 및 유효성 검증 수행 ===")
    
    # price_new.xlsx에서 매칭 키와 가격 정보 딕셔너리 생성
    price_dict = {}
    for _, row in df_price.iterrows():
        key = create_matching_key(row)
        price_dict[key] = {
            '수리_부품교체': row['수리_부품교체'],
            '수리_세척': row['수리_세척'],
            '수리_SW': row['수리_SW'],
            '수리_기타': row['수리_기타'],
            '검수': row['검수']
        }
    
    print(f"price_new.xlsx에서 생성된 매칭 키: {len(price_dict)}개")
    
    # 결과 데이터 리스트
    result_data = []
    
    # 매칭 통계
    매칭_성공 = 0
    매칭_실패 = 0
    유효성_통과 = 0
    유효성_실패 = 0
    
    # 4.xlsx의 각 항목에 대해 매칭 및 검증 수행
    for idx, row in df_4xlsx.iterrows():
        qaid = row['QAID']
        key = create_matching_key(row)
        수리결과 = row['수리결과']
        
        # 매칭 시도
        if key in price_dict:
            매칭_성공 += 1
            매칭상태 = "매칭됨"
            
            # 해당 수리결과의 가격 정보 가져오기
            price_info = price_dict[key]
            
            if 수리결과 in price_info:
                예상가격 = price_info[수리결과]
                유효성_통과 += 1
                유효성검증 = "통과"
            else:
                예상가격 = "해당없음"
                유효성_실패 += 1
                유효성검증 = "실패 - 수리결과 불일치"
            
            # 모든 가격 정보 포함
            result_data.append({
                'QAID': qaid,
                '4차': row['4차'],
                '5차': row['5차'],
                '상품단가구간': row['상품단가구간'],
                '수리결과': 수리결과,
                '매칭상태': 매칭상태,
                '예상가격': 예상가격,
                '수리_부품교체': price_info['수리_부품교체'],
                '수리_세척': price_info['수리_세척'],
                '수리_SW': price_info['수리_SW'],
                '수리_기타': price_info['수리_기타'],
                '검수': price_info['검수'],
                '유효성검증': 유효성검증
            })
        else:
            매칭_실패 += 1
            유효성_실패 += 1
            
            result_data.append({
                'QAID': qaid,
                '4차': row['4차'],
                '5차': row['5차'],
                '상품단가구간': row['상품단가구간'],
                '수리결과': 수리결과,
                '매칭상태': "별도항목 - 매칭실패",
                '예상가격': "매칭실패",
                '수리_부품교체': "매칭실패",
                '수리_세척': "매칭실패",
                '수리_SW': "매칭실패",
                '수리_기타': "매칭실패",
                '검수': "매칭실패",
                '유효성검증': "실패 - 매칭불가"
            })
        
        # 진행상황 출력 (처음 10개)
        if idx < 10:
            print(f"처리 {idx+1}: {key} -> {매칭상태}")
    
    # DataFrame으로 변환
    result_df = pd.DataFrame(result_data)
    
    # 통계 출력
    print(f"\n=== 매칭 및 검증 통계 ===")
    print(f"총 처리 항목: {len(result_df)}")
    print(f"매칭 성공: {매칭_성공}개 ({매칭_성공/len(result_df)*100:.1f}%)")
    print(f"매칭 실패: {매칭_실패}개 ({매칭_실패/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 통과: {유효성_통과}개 ({유효성_통과/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 실패: {유효성_실패}개 ({유효성_실패/len(result_df)*100:.1f}%)")
    
    return result_df

def analyze_results(result_df):
    """결과 분석을 수행합니다."""
    print(f"\n=== 결과 분석 ===")
    
    # 매칭 상태별 통계
    print("매칭 상태별 통계:")
    매칭상태_통계 = result_df['매칭상태'].value_counts()
    for 상태, 개수 in 매칭상태_통계.items():
        print(f"  {상태}: {개수}개")
    
    # 유효성 검증 결과별 통계
    print("\n유효성 검증 결과별 통계:")
    유효성검증_통계 = result_df['유효성검증'].value_counts()
    for 결과, 개수 in 유효성검증_통계.items():
        print(f"  {결과}: {개수}개")
    
    # 수리결과별 매칭 성공률
    print("\n수리결과별 매칭 성공률:")
    수리결과별_통계 = result_df.groupby('수리결과').agg({
        '매칭상태': lambda x: (x == '매칭됨').sum(),
        'QAID': 'count'
    }).rename(columns={'매칭상태': '매칭성공', 'QAID': '총개수'})
    수리결과별_통계['성공률'] = (수리결과별_통계['매칭성공'] / 수리결과별_통계['총개수'] * 100).round(1)
    print(수리결과별_통계)

def main():
    """메인 실행 함수"""
    print("4.xlsx와 price_new.xlsx 매칭 및 유효성 검증 프로그램")
    print("=" * 60)
    
    # 파일 로드
    df_4xlsx = load_4xlsx()
    df_price = load_price_new()
    
    # 매칭 및 유효성 검증 수행
    result_df = perform_matching_and_validation(df_4xlsx, df_price)
    
    # 결과 분석
    analyze_results(result_df)
    
    # 결과 미리보기
    print(f"\n=== 결과 미리보기 (처음 10개) ===")
    print(result_df.head(10))
    
    # 별도항목 (매칭 실패) 미리보기
    별도항목 = result_df[result_df['매칭상태'].str.contains('별도항목')]
    if not 별도항목.empty:
        print(f"\n=== 별도항목 (매칭 실패) 미리보기 (처음 5개) ===")
        print(별도항목[['QAID', '4차', '5차', '상품단가구간', '수리결과', '매칭상태']].head())
    
    # 결과 파일 저장
    output_filename = "가격_매칭_유효성검증_결과.xlsx"
    
    # 여러 시트로 저장
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        # 전체 결과
        result_df.to_excel(writer, sheet_name='전체결과', index=False)
        
        # 매칭 성공 항목만
        매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
        매칭성공.to_excel(writer, sheet_name='매칭성공', index=False)
        
        # 별도항목 (매칭 실패)
        if not 별도항목.empty:
            별도항목.to_excel(writer, sheet_name='별도항목_매칭실패', index=False)
        
        # 유효성 검증 실패 항목
        유효성실패 = result_df[result_df['유효성검증'].str.contains('실패')]
        if not 유효성실패.empty:
            유효성실패.to_excel(writer, sheet_name='유효성검증실패', index=False)
    
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    print("시트 구성: 전체결과, 매칭성공, 별도항목_매칭실패, 유효성검증실패")

if __name__ == "__main__":
    main()
