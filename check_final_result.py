import pandas as pd

# 최종 결과 파일 확인
df_result = pd.read_excel("매칭_결과_최종.xlsx")

print("=== 최종 매칭 결과 파일 내용 ===")
print(df_result)

print(f"\n=== 상세 정보 ===")
print(f"총 카테고리 수: {len(df_result)}")

print(f"\n=== 각 카테고리별 상세 정보 ===")
for idx, row in df_result.iterrows():
    print(f"\n카테고리 {idx+1}: {row['처리내용']}")
    print(f"  - 수리결과: {row['수리결과']}")
    print(f"  - 수량: {row['수량']:,}개")
    print(f"  - 수리비용: {row['수리비용']:,}원")
    print(f"  - 별도수리비: {row['별도수리비']:,}원")
    print(f"  - 추가비용: {row['추가비용']:,}원")
