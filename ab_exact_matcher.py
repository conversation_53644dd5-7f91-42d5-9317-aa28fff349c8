import pandas as pd
import numpy as np

def load_a_file():
    """a.xlsx 파일을 로드합니다."""
    print("=== a.xlsx 파일 로드 ===")
    
    # 헤더 행 1로 로드
    df = pd.read_excel("a.xlsx", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['제품명', '구간', '수리_부품교체', '수리_세척', '수리_기타', '검수']
    df_selected = df[required_columns].copy()
    
    # NaN 값이 있는 행 제거
    df_selected = df_selected.dropna(subset=['제품명']).reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"제품명 고유값: {df_selected['제품명'].nunique()}개")
    print("데이터 샘플:")
    print(df_selected.head())
    
    return df_selected

def load_b_file():
    """b.xlsx 파일을 로드합니다."""
    print("\n=== b.xlsx 파일 로드 ===")
    
    # 헤더 행 1로 로드
    df = pd.read_excel("b.xlsx", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['bar_cd', '제품명']
    df_selected = df[required_columns].copy()
    
    # NaN 값이 있는 행 제거
    df_selected = df_selected.dropna(subset=['제품명']).reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"제품명 고유값: {df_selected['제품명'].nunique()}개")
    print("데이터 샘플:")
    print(df_selected.head())
    
    return df_selected

def normalize_product_name(product_name):
    """제품명을 정규화합니다."""
    if pd.isna(product_name):
        return ""
    
    # 문자열로 변환하고 앞뒤 공백 제거
    normalized = str(product_name).strip()
    
    return normalized

def create_exact_match_mapping(df_a, df_b):
    """정확한 제품명 매칭을 위한 매핑을 생성합니다."""
    print("\n=== 정확한 제품명 매칭 매핑 생성 ===")
    
    # a.xlsx의 제품명 딕셔너리 생성
    a_dict = {}
    for idx, row in df_a.iterrows():
        제품명 = normalize_product_name(row['제품명'])
        if 제품명:
            a_dict[제품명] = {
                '구간': row['구간'],
                '수리_부품교체': row['수리_부품교체'],
                '수리_세척': row['수리_세척'],
                '수리_기타': row['수리_기타'],
                '검수': row['검수']
            }
    
    # b.xlsx의 제품명 딕셔너리 생성
    b_dict = {}
    for idx, row in df_b.iterrows():
        제품명 = normalize_product_name(row['제품명'])
        if 제품명:
            if 제품명 not in b_dict:
                b_dict[제품명] = []
            b_dict[제품명].append({
                'bar_cd': row['bar_cd'],
                'b_index': idx
            })
    
    print(f"a.xlsx 제품명 딕셔너리: {len(a_dict)}개")
    print(f"b.xlsx 제품명 딕셔너리: {len(b_dict)}개")
    
    return a_dict, b_dict

def perform_exact_matching(df_a, df_b):
    """정확한 제품명 매칭을 수행합니다."""
    print("\n=== 정확한 제품명 매칭 수행 ===")
    
    # 매핑 생성
    a_dict, b_dict = create_exact_match_mapping(df_a, df_b)
    
    # 결과 데이터 리스트
    result_data = []
    
    # 매칭 통계
    매칭_성공 = 0
    a_미매칭 = 0
    b_미매칭 = 0
    
    # 매칭된 b.xlsx 항목 추적
    matched_b_items = set()
    
    print("매칭 진행 중...")
    
    # a.xlsx 기준으로 매칭
    for idx, row_a in df_a.iterrows():
        제품명_a = normalize_product_name(row_a['제품명'])
        
        if 제품명_a in b_dict:
            # 매칭 성공
            매칭_성공 += 1
            
            # b.xlsx에서 해당 제품명의 모든 항목과 매칭
            for b_item in b_dict[제품명_a]:
                matched_b_items.add(b_item['b_index'])
                
                result_data.append({
                    '출처': 'A+B 매칭',
                    'A_제품명': row_a['제품명'],
                    'B_제품명': row_a['제품명'],  # 정확히 일치하므로 동일
                    'bar_cd': b_item['bar_cd'],
                    '구간': row_a['구간'],
                    '수리_부품교체': row_a['수리_부품교체'],
                    '수리_세척': row_a['수리_세척'],
                    '수리_기타': row_a['수리_기타'],
                    '검수': row_a['검수'],
                    '매칭상태': '매칭됨'
                })
        else:
            # a.xlsx에만 있는 항목
            a_미매칭 += 1
            
            result_data.append({
                '출처': 'A 전용',
                'A_제품명': row_a['제품명'],
                'B_제품명': '미매칭',
                'bar_cd': '미매칭',
                '구간': row_a['구간'],
                '수리_부품교체': row_a['수리_부품교체'],
                '수리_세척': row_a['수리_세척'],
                '수리_기타': row_a['수리_기타'],
                '검수': row_a['검수'],
                '매칭상태': 'A 전용 - 미매칭'
            })
        
        # 진행상황 출력
        if (idx + 1) % 200 == 0:
            print(f"A 파일 진행률: {idx + 1}/{len(df_a)} ({(idx + 1)/len(df_a)*100:.1f}%)")
    
    # b.xlsx에만 있는 항목들 추가
    print("B 전용 항목 추가 중...")
    for idx, row_b in df_b.iterrows():
        if idx not in matched_b_items:
            b_미매칭 += 1
            
            result_data.append({
                '출처': 'B 전용',
                'A_제품명': '미매칭',
                'B_제품명': row_b['제품명'],
                'bar_cd': row_b['bar_cd'],
                '구간': '미매칭',
                '수리_부품교체': '미매칭',
                '수리_세척': '미매칭',
                '수리_기타': '미매칭',
                '검수': '미매칭',
                '매칭상태': 'B 전용 - 미매칭'
            })
        
        # 진행상황 출력
        if (idx + 1) % 10000 == 0:
            print(f"B 파일 진행률: {idx + 1}/{len(df_b)} ({(idx + 1)/len(df_b)*100:.1f}%)")
    
    # DataFrame으로 변환
    result_df = pd.DataFrame(result_data)
    
    # 통계 출력
    print(f"\n=== 매칭 통계 ===")
    print(f"총 결과 항목: {len(result_df)}")
    print(f"A+B 매칭 성공: {len(result_df[result_df['출처'] == 'A+B 매칭'])}개")
    print(f"A 전용 (미매칭): {len(result_df[result_df['출처'] == 'A 전용'])}개")
    print(f"B 전용 (미매칭): {len(result_df[result_df['출처'] == 'B 전용'])}개")
    
    return result_df

def analyze_results(result_df):
    """결과 분석을 수행합니다."""
    print(f"\n=== 결과 분석 ===")
    
    # 출처별 통계
    print("출처별 통계:")
    출처별_통계 = result_df['출처'].value_counts()
    for 출처, 개수 in 출처별_통계.items():
        print(f"  {출처}: {개수}개")
    
    # 매칭 상태별 통계
    print("\n매칭 상태별 통계:")
    매칭상태_통계 = result_df['매칭상태'].value_counts()
    for 상태, 개수 in 매칭상태_통계.items():
        print(f"  {상태}: {개수}개")
    
    # 매칭률 계산
    총_a_항목 = len(result_df[(result_df['출처'] == 'A+B 매칭') | (result_df['출처'] == 'A 전용')])
    총_b_항목 = len(result_df[(result_df['출처'] == 'A+B 매칭') | (result_df['출처'] == 'B 전용')])
    매칭_항목 = len(result_df[result_df['출처'] == 'A+B 매칭'])
    
    print(f"\n매칭률:")
    print(f"  A 파일 기준 매칭률: {매칭_항목/총_a_항목*100:.1f}% ({매칭_항목}/{총_a_항목})")
    print(f"  B 파일 기준 매칭률: {매칭_항목/총_b_항목*100:.1f}% ({매칭_항목}/{총_b_항목})")

def main():
    """메인 실행 함수"""
    print("a.xlsx와 b.xlsx 제품명 정확 매칭 프로그램")
    print("=" * 60)
    
    # 파일 로드
    df_a = load_a_file()
    df_b = load_b_file()
    
    # 정확한 매칭 수행
    result_df = perform_exact_matching(df_a, df_b)
    
    # 결과 분석
    analyze_results(result_df)
    
    # 결과 미리보기
    print(f"\n=== 결과 미리보기 (처음 10개) ===")
    display_columns = ['출처', 'A_제품명', 'B_제품명', 'bar_cd', '구간', '매칭상태']
    print(result_df[display_columns].head(10))
    
    # 매칭 성공 사례 미리보기
    매칭성공 = result_df[result_df['출처'] == 'A+B 매칭']
    if not 매칭성공.empty:
        print(f"\n=== 매칭 성공 사례 (처음 5개) ===")
        print(매칭성공[display_columns].head())
    
    # A 전용 항목 미리보기
    a_전용 = result_df[result_df['출처'] == 'A 전용']
    if not a_전용.empty:
        print(f"\n=== A 전용 항목 (처음 5개) ===")
        print(a_전용[['A_제품명', '구간', '매칭상태']].head())
    
    # B 전용 항목 미리보기
    b_전용 = result_df[result_df['출처'] == 'B 전용']
    if not b_전용.empty:
        print(f"\n=== B 전용 항목 (처음 5개) ===")
        print(b_전용[['B_제품명', 'bar_cd', '매칭상태']].head())
    
    # 결과 파일 저장
    output_filename = "AB_제품명_정확매칭_결과.xlsx"
    
    # 여러 시트로 저장
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        # 전체 결과
        result_df.to_excel(writer, sheet_name='전체결과', index=False)
        
        # 매칭 성공 항목
        if not 매칭성공.empty:
            매칭성공.to_excel(writer, sheet_name='매칭성공', index=False)
        
        # A 전용 항목
        if not a_전용.empty:
            a_전용.to_excel(writer, sheet_name='A전용_미매칭', index=False)
        
        # B 전용 항목
        if not b_전용.empty:
            b_전용.to_excel(writer, sheet_name='B전용_미매칭', index=False)
    
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    print("시트 구성: 전체결과, 매칭성공, A전용_미매칭, B전용_미매칭")

if __name__ == "__main__":
    main()
