import pandas as pd
import numpy as np

def load_file1():
    """1.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("1.xlsx", header=None)

    # 첫 번째 행에 헤더가 있으므로 이를 제거하고 컬럼명 설정
    df = df.iloc[1:].reset_index(drop=True)  # 첫 번째 행(헤더) 제거
    df.columns = ['빈컬럼', '처리내용', '수리결과']

    # 빈 컬럼 제거
    df = df[['처리내용', '수리결과']].copy()

    # 헤더 행 제거 (처리내용이 '처리내용'인 행)
    df = df[df['처리내용'] != '처리내용'].reset_index(drop=True)

    # NaN 값 제거
    df = df.dropna().reset_index(drop=True)

    print("=== 1.xlsx 데이터 ===")
    print(df)
    print(f"총 {len(df)}개 항목")

    return df

def load_file2():
    """2.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("2.xlsx", header=None)

    # 데이터 구조 파악 후 필요한 부분만 추출
    # 행 레이블이 있는 부분부터 시작 (row 2부터)
    df_clean = df.iloc[2:].reset_index(drop=True)
    df_clean.columns = ['처리내용', '수량', '수리비용', '별도수리비', '추가비용']

    # 헤더 행 제거 ('행 레이블', '합계 : 수량' 등이 포함된 행)
    df_clean = df_clean[df_clean['처리내용'] != '행 레이블'].reset_index(drop=True)

    # 총합계 행 제거
    df_clean = df_clean[df_clean['처리내용'] != '총합계'].reset_index(drop=True)

    # NaN 값 제거
    df_clean = df_clean.dropna(subset=['처리내용']).reset_index(drop=True)

    print("\n=== 2.xlsx 데이터 ===")
    print(df_clean)
    print(f"총 {len(df_clean)}개 항목")

    return df_clean

def create_mapping():
    """처리내용 매핑 규칙을 정의합니다."""
    # 1.xlsx의 수리결과 카테고리와 2.xlsx의 처리내용 카테고리 매핑
    mapping = {
        'XL': ['가공 불가'],
        '리퍼완료': ['오염부 리퍼 완료', '정상 확인 및 클리닝 작업'],
        '점검완료(중)': ['구성품 완성', '수리 또는 부품 교체', '전원부 수리 또는 교체', 'S/W 재설정']
    }
    
    # 역방향 매핑도 생성 (1.xlsx 처리내용 -> 2.xlsx 카테고리)
    reverse_mapping = {}
    for category, items in mapping.items():
        for item in items:
            reverse_mapping[item] = category
    
    return mapping, reverse_mapping

def match_and_merge(df1, df2):
    """두 데이터프레임을 매칭하여 결합합니다."""
    mapping, reverse_mapping = create_mapping()
    
    # 결과 데이터프레임 초기화
    result_data = []
    
    print("\n=== 매칭 과정 ===")
    
    # 1.xlsx의 각 항목에 대해 처리
    for idx, row in df1.iterrows():
        처리내용 = row['처리내용']
        수리결과 = row['수리결과']
        
        # 처리내용에서 키워드 매칭
        matched_category = None
        for keyword, category in reverse_mapping.items():
            if keyword in 처리내용:
                matched_category = category
                break
        
        # 2.xlsx에서 해당 카테고리 찾기
        수량 = 0
        수리비용 = 0
        별도수리비 = 0
        추가비용 = 0
        
        if matched_category:
            matching_row = df2[df2['처리내용'] == matched_category]
            if not matching_row.empty:
                수량 = matching_row.iloc[0]['수량']
                수리비용 = matching_row.iloc[0]['수리비용']
                별도수리비 = matching_row.iloc[0]['별도수리비']
                추가비용 = matching_row.iloc[0]['추가비용']
                print(f"매칭됨: '{처리내용}' -> '{matched_category}'")
            else:
                print(f"매칭 실패: '{처리내용}' -> '{matched_category}' (2.xlsx에 없음)")
        else:
            print(f"매칭 실패: '{처리내용}' (매핑 규칙 없음)")
        
        result_data.append({
            '출처': '1.xlsx',
            '처리내용': 처리내용,
            '수리결과': 수리결과,
            '매칭된_카테고리': matched_category,
            '수량': 수량,
            '수리비용': 수리비용,
            '별도수리비': 별도수리비,
            '추가비용': 추가비용
        })
    
    # 2.xlsx의 항목들도 추가 (1.xlsx에 없는 것들)
    for idx, row in df2.iterrows():
        카테고리 = row['처리내용']
        
        # 이미 1.xlsx에서 매칭된 카테고리인지 확인
        already_matched = any(item['매칭된_카테고리'] == 카테고리 for item in result_data)
        
        if not already_matched:
            result_data.append({
                '출처': '2.xlsx',
                '처리내용': f"[{카테고리}] 카테고리",
                '수리결과': 카테고리,
                '매칭된_카테고리': 카테고리,
                '수량': row['수량'],
                '수리비용': row['수리비용'],
                '별도수리비': row['별도수리비'],
                '추가비용': row['추가비용']
            })
            print(f"2.xlsx 전용 항목 추가: '{카테고리}'")
    
    return pd.DataFrame(result_data)

def main():
    """메인 실행 함수"""
    print("Excel 파일 매칭 프로그램 시작")
    
    # 파일 로드
    df1 = load_file1()
    df2 = load_file2()
    
    # 매칭 및 병합
    result_df = match_and_merge(df1, df2)
    
    # 결과 출력
    print("\n=== 최종 결과 ===")
    print(result_df)
    
    # 결과 파일 저장
    output_filename = "매칭_결과.xlsx"
    result_df.to_excel(output_filename, index=False)
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    
    # 요약 통계
    print(f"\n=== 요약 ===")
    print(f"총 항목 수: {len(result_df)}")
    print(f"1.xlsx 출처 항목: {len(result_df[result_df['출처'] == '1.xlsx'])}")
    print(f"2.xlsx 출처 항목: {len(result_df[result_df['출처'] == '2.xlsx'])}")
    print(f"매칭 성공 항목: {len(result_df[result_df['매칭된_카테고리'].notna()])}")

if __name__ == "__main__":
    main()
