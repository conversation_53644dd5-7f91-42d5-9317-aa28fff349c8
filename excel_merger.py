import pandas as pd
import numpy as np

def load_file1():
    """1.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("1.xlsx", header=None)

    # 첫 번째 행에 헤더가 있으므로 이를 제거하고 컬럼명 설정
    df = df.iloc[1:].reset_index(drop=True)  # 첫 번째 행(헤더) 제거
    df.columns = ['빈컬럼', '처리내용', '수리결과']

    # 빈 컬럼 제거
    df = df[['처리내용', '수리결과']].copy()

    # 헤더 행 제거 (처리내용이 '처리내용'인 행)
    df = df[df['처리내용'] != '처리내용'].reset_index(drop=True)

    # NaN 값 제거
    df = df.dropna().reset_index(drop=True)

    print("=== 1.xlsx 데이터 ===")
    print(df)
    print(f"총 {len(df)}개 항목")

    return df

def load_file2():
    """2.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("2.xlsx", header=None)

    # 데이터 구조 파악 후 필요한 부분만 추출
    # 행 레이블이 있는 부분부터 시작 (row 2부터)
    df_clean = df.iloc[2:].reset_index(drop=True)
    df_clean.columns = ['처리내용', '수량', '수리비용', '별도수리비', '추가비용']

    # 헤더 행 제거 ('행 레이블', '합계 : 수량' 등이 포함된 행)
    df_clean = df_clean[df_clean['처리내용'] != '행 레이블'].reset_index(drop=True)

    # 총합계 행 제거
    df_clean = df_clean[df_clean['처리내용'] != '총합계'].reset_index(drop=True)

    # NaN 값 제거
    df_clean = df_clean.dropna(subset=['처리내용']).reset_index(drop=True)

    print("\n=== 2.xlsx 데이터 ===")
    print(df_clean)
    print(f"총 {len(df_clean)}개 항목")

    return df_clean

def create_mapping():
    """처리내용 매핑 규칙을 정의합니다."""
    # 1.xlsx의 수리결과 카테고리와 2.xlsx의 처리내용 카테고리 매핑
    mapping = {
        'XL': ['가공 불가'],
        '리퍼완료': ['오염부 리퍼 완료', '정상 확인 및 클리닝 작업'],
        '점검완료(중)': ['구성품 완성', '수리 또는 부품 교체', '전원부 수리 또는 교체', 'S/W 재설정']
    }
    
    # 역방향 매핑도 생성 (1.xlsx 처리내용 -> 2.xlsx 카테고리)
    reverse_mapping = {}
    for category, items in mapping.items():
        for item in items:
            reverse_mapping[item] = category
    
    return mapping, reverse_mapping

def match_and_merge(df1, df2):
    """두 데이터프레임을 매칭하여 결합합니다."""
    mapping, reverse_mapping = create_mapping()

    # 결과 데이터프레임 초기화
    result_data = []

    print("\n=== 매칭 과정 ===")

    # 2.xlsx의 각 카테고리에 대해 처리 (기준을 2.xlsx로 변경)
    for idx, row2 in df2.iterrows():
        카테고리 = row2['처리내용']
        수량 = row2['수량']
        수리비용 = row2['수리비용']
        별도수리비 = row2['별도수리비']
        추가비용 = row2['추가비용']

        print(f"\n--- 2.xlsx 카테고리: '{카테고리}' 처리 중 ---")

        # 해당 카테고리와 매칭되는 1.xlsx 항목들 찾기
        matched_items = []

        # 매핑 규칙에 따라 1.xlsx에서 매칭되는 항목들 찾기
        if 카테고리 in mapping:
            keywords = mapping[카테고리]
            for keyword in keywords:
                matching_rows = df1[df1['처리내용'].str.contains(keyword, na=False)]
                for _, row1 in matching_rows.iterrows():
                    matched_items.append({
                        '1xlsx_처리내용': row1['처리내용'],
                        '1xlsx_수리결과': row1['수리결과']
                    })
                    print(f"  매칭됨: '{row1['처리내용']}' -> '{카테고리}'")

        # 매칭된 항목이 있으면 각각을 별도 행으로 추가
        if matched_items:
            for item in matched_items:
                result_data.append({
                    '2xlsx_카테고리': 카테고리,
                    '2xlsx_수량': 수량,
                    '2xlsx_수리비용': 수리비용,
                    '2xlsx_별도수리비': 별도수리비,
                    '2xlsx_추가비용': 추가비용,
                    '1xlsx_처리내용': item['1xlsx_처리내용'],
                    '1xlsx_수리결과': item['1xlsx_수리결과'],
                    '매칭상태': '매칭됨'
                })
        else:
            # 매칭되지 않은 2.xlsx 항목도 표시
            result_data.append({
                '2xlsx_카테고리': 카테고리,
                '2xlsx_수량': 수량,
                '2xlsx_수리비용': 수리비용,
                '2xlsx_별도수리비': 별도수리비,
                '2xlsx_추가비용': 추가비용,
                '1xlsx_처리내용': '매칭 항목 없음',
                '1xlsx_수리결과': '매칭 항목 없음',
                '매칭상태': '매칭 안됨'
            })
            print(f"  매칭 안됨: '{카테고리}' (1.xlsx에 해당 항목 없음)")

    # 1.xlsx에만 있고 2.xlsx에 매칭되지 않은 항목들 추가
    print(f"\n--- 1.xlsx 전용 항목 확인 ---")
    for idx, row1 in df1.iterrows():
        처리내용 = row1['처리내용']
        수리결과 = row1['수리결과']

        # 이 항목이 이미 매칭되었는지 확인
        already_matched = False
        for keyword, category in reverse_mapping.items():
            if keyword in 처리내용:
                already_matched = True
                break

        if not already_matched:
            result_data.append({
                '2xlsx_카테고리': '매칭 항목 없음',
                '2xlsx_수량': 0,
                '2xlsx_수리비용': 0,
                '2xlsx_별도수리비': 0,
                '2xlsx_추가비용': 0,
                '1xlsx_처리내용': 처리내용,
                '1xlsx_수리결과': 수리결과,
                '매칭상태': '1.xlsx 전용'
            })
            print(f"  1.xlsx 전용: '{처리내용}'")

    return pd.DataFrame(result_data)

def main():
    """메인 실행 함수"""
    print("Excel 파일 매칭 프로그램 시작")
    
    # 파일 로드
    df1 = load_file1()
    df2 = load_file2()
    
    # 매칭 및 병합
    result_df = match_and_merge(df1, df2)
    
    # 결과 출력
    print("\n=== 최종 결과 ===")
    print(result_df)
    
    # 결과 파일 저장
    output_filename = "매칭_결과.xlsx"
    result_df.to_excel(output_filename, index=False)
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    
    # 요약 통계
    print(f"\n=== 요약 ===")
    print(f"총 항목 수: {len(result_df)}")
    print(f"매칭됨: {len(result_df[result_df['매칭상태'] == '매칭됨'])}")
    print(f"매칭 안됨 (2.xlsx 전용): {len(result_df[result_df['매칭상태'] == '매칭 안됨'])}")
    print(f"1.xlsx 전용: {len(result_df[result_df['매칭상태'] == '1.xlsx 전용'])}")

    # 카테고리별 매칭 현황
    print(f"\n=== 카테고리별 매칭 현황 ===")
    category_summary = result_df.groupby('2xlsx_카테고리').agg({
        '매칭상태': 'count',
        '2xlsx_수량': 'first',
        '2xlsx_수리비용': 'first'
    }).rename(columns={'매칭상태': '매칭된_항목수'})
    print(category_summary)

if __name__ == "__main__":
    main()
