import pandas as pd

# 최종 결과 파일 확인
df_result = pd.read_excel("QAID_처리내용_수리결과_매칭.xlsx")

print("=== 최종 매칭 결과 파일 확인 ===")
print(f"총 데이터 수: {len(df_result):,}개")
print(f"컬럼: {list(df_result.columns)}")

print(f"\n=== 처음 20개 데이터 샘플 ===")
print(df_result.head(20))

print(f"\n=== 처리내용별 매칭 결과 통계 ===")
처리내용_수리결과 = df_result.groupby(['처리내용', '수리결과']).size().reset_index(name='개수')
print(처리내용_수리결과)

print(f"\n=== 수리결과별 총 개수 ===")
수리결과_통계 = df_result['수리결과'].value_counts()
for 결과, 개수 in 수리결과_통계.items():
    print(f"{결과}: {개수:,}개")

print(f"\n=== 매칭 성공률 ===")
총개수 = len(df_result)
매칭성공 = len(df_result[df_result['수리결과'] != '매칭 항목 없음'])
print(f"총 항목: {총개수:,}개")
print(f"매칭 성공: {매칭성공:,}개")
print(f"매칭 성공률: {매칭성공/총개수*100:.1f}%")
