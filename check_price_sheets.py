import pandas as pd
import openpyxl

def check_all_sheets():
    """price_new.xlsx의 모든 시트를 확인합니다."""
    print("=== price_new.xlsx 모든 시트 확인 ===")
    
    try:
        wb = openpyxl.load_workbook("price_new.xlsx")
        print(f"전체 시트 목록: {wb.sheetnames}")
        
        for sheet_name in wb.sheetnames:
            print(f"\n--- {sheet_name} 시트 ---")
            df = pd.read_excel("price_new.xlsx", sheet_name=sheet_name, header=None)
            print(f"Shape: {df.shape}")
            
            # 첫 5행 확인
            print("첫 5행:")
            for i in range(min(5, len(df))):
                row_data = []
                for j in range(min(8, len(df.columns))):
                    cell_value = str(df.iloc[i, j])[:30]
                    row_data.append(f"[{j}]{cell_value}")
                print(f"  행 {i}: {' | '.join(row_data)}")
            
            # TV 관련 키워드 검색
            tv_keywords = ['TV', 'tv', '티비', '텔레비전', 'TCL', '이노스', '와이드뷰', '프리즘']
            tv_found = False
            
            for keyword in tv_keywords:
                for i in range(min(20, len(df))):
                    for j in range(len(df.columns)):
                        cell_value = str(df.iloc[i, j])
                        if keyword in cell_value:
                            if not tv_found:
                                print(f"  TV 관련 키워드 '{keyword}' 발견!")
                                tv_found = True
                            break
                    if tv_found:
                        break
                if tv_found:
                    break
        
        wb.close()
        
    except Exception as e:
        print(f"오류: {e}")

def check_tv_products_in_4xlsx():
    """4.xlsx에서 TV 제품들 확인"""
    print("\n=== 4.xlsx TV 제품 샘플 확인 ===")
    
    df = pd.read_excel("4.xlsx", header=1)
    tv_products = df[(df['4차'] == 'TV') & (df['5차'] == 'TV')]
    
    print(f"TV 제품 총 개수: {len(tv_products)}")
    print("TV 제품 샘플 (처음 10개):")
    for i, (_, row) in enumerate(tv_products.head(10).iterrows()):
        print(f"  {i+1}. {row['제품명'][:80]}...")

if __name__ == "__main__":
    check_all_sheets()
    check_tv_products_in_4xlsx()
