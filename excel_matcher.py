import pandas as pd

# Read the Excel files
df1 = pd.read_excel("1.xlsx")
df2 = pd.read_excel("2.xlsx")

# Merge the dataframes on "처리내용" column
# Using outer join to include all items from both files
result = pd.merge(df1, df2, on="처리내용", how="outer", suffixes=("_file1", "_file2"))

# Reorder columns to show key information first
cols = ["처리내용"]
if "수리결과" in df2.columns:
    cols.append("수리결과")
cols.extend([col for col in result.columns if col not in cols])
result = result[cols]

# Save the result
result.to_excel("결과파일.xlsx", index=False)
print("결과파일.xlsx가 생성되었습니다.")