import pandas as pd

def check_validation_results():
    """유효성 검증 결과를 확인합니다."""
    print("=== 가격 매칭 유효성 검증 결과 확인 ===")
    
    try:
        # 전체 결과 확인
        df_전체 = pd.read_excel("가격_매칭_유효성검증_결과.xlsx", sheet_name="전체결과")
        print(f"전체 결과: {len(df_전체):,}개 항목")
        
        # 매칭 성공 확인
        df_매칭성공 = pd.read_excel("가격_매칭_유효성검증_결과.xlsx", sheet_name="매칭성공")
        print(f"매칭 성공: {len(df_매칭성공):,}개 항목")
        
        # 별도항목 확인
        df_별도항목 = pd.read_excel("가격_매칭_유효성검증_결과.xlsx", sheet_name="별도항목_매칭실패")
        print(f"별도항목 (매칭실패): {len(df_별도항목):,}개 항목")
        
        # 유효성 검증 실패 확인
        df_유효성실패 = pd.read_excel("가격_매칭_유효성검증_결과.xlsx", sheet_name="유효성검증실패")
        print(f"유효성 검증 실패: {len(df_유효성실패):,}개 항목")
        
        print(f"\n=== 상세 통계 ===")
        
        # 매칭 성공률
        매칭성공률 = len(df_매칭성공) / len(df_전체) * 100
        print(f"매칭 성공률: {매칭성공률:.1f}%")
        
        # 유효성 검증 통과율
        유효성통과 = len(df_전체[df_전체['유효성검증'] == '통과'])
        유효성통과율 = 유효성통과 / len(df_전체) * 100
        print(f"유효성 검증 통과율: {유효성통과율:.1f}%")
        
        # 수리결과별 상세 분석
        print(f"\n=== 수리결과별 분석 ===")
        수리결과_분석 = df_전체.groupby('수리결과').agg({
            '매칭상태': lambda x: (x == '매칭됨').sum(),
            '유효성검증': lambda x: (x == '통과').sum(),
            'QAID': 'count'
        }).rename(columns={'매칭상태': '매칭성공', '유효성검증': '유효성통과', 'QAID': '총개수'})
        
        수리결과_분석['매칭성공률'] = (수리결과_분석['매칭성공'] / 수리결과_분석['총개수'] * 100).round(1)
        수리결과_분석['유효성통과율'] = (수리결과_분석['유효성통과'] / 수리결과_분석['총개수'] * 100).round(1)
        
        print(수리결과_분석)
        
        # 별도항목 상세 분석
        print(f"\n=== 별도항목 (매칭실패) 상세 분석 ===")
        if not df_별도항목.empty:
            print("4차 카테고리별 매칭 실패 현황:")
            별도항목_4차 = df_별도항목['4차'].value_counts().head(10)
            for 카테고리, 개수 in 별도항목_4차.items():
                print(f"  {카테고리}: {개수}개")
            
            print("\n5차 카테고리별 매칭 실패 현황 (상위 10개):")
            별도항목_5차 = df_별도항목['5차'].value_counts().head(10)
            for 카테고리, 개수 in 별도항목_5차.items():
                print(f"  {카테고리}: {개수}개")
        
        # 가격 정보 샘플
        print(f"\n=== 매칭 성공 항목 가격 정보 샘플 ===")
        매칭성공_샘플 = df_매칭성공[['QAID', '4차', '5차', '수리결과', '예상가격']].head(10)
        print(매칭성공_샘플)
        
        return True
        
    except Exception as e:
        print(f"결과 확인 오류: {e}")
        return False

if __name__ == "__main__":
    check_validation_results()
