import pandas as pd
import numpy as np

def load_file1():
    """1.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("1.xlsx", header=None)
    
    # 첫 번째 행에 헤더가 있으므로 이를 제거하고 컬럼명 설정
    df = df.iloc[1:].reset_index(drop=True)  # 첫 번째 행(헤더) 제거
    df.columns = ['빈컬럼', '처리내용', '수리결과']
    
    # 빈 컬럼 제거
    df = df[['처리내용', '수리결과']].copy()
    
    # 헤더 행 제거 (처리내용이 '처리내용'인 행)
    df = df[df['처리내용'] != '처리내용'].reset_index(drop=True)
    
    # NaN 값 제거
    df = df.dropna().reset_index(drop=True)
    
    print("=== 1.xlsx 데이터 ===")
    print(df)
    print(f"총 {len(df)}개 항목")
    
    return df

def load_file2():
    """2.xlsx 파일에서 QAID와 처리내용 데이터를 로드합니다."""
    # Sheet1에서 QAID와 처리내용 로드
    df = pd.read_excel("2.xlsx", sheet_name="Sheet1", header=1)  # 2번째 행이 헤더
    
    # NaN 값 제거
    df = df.dropna().reset_index(drop=True)
    
    print("\n=== 2.xlsx 데이터 (Sheet1) ===")
    print(f"컬럼명: {list(df.columns)}")
    print(df.head(10))
    print(f"총 {len(df)}개 항목")
    
    # 처리내용별 통계
    print(f"\n=== 처리내용별 통계 ===")
    처리내용_통계 = df['처리내용'].value_counts()
    print(처리내용_통계)
    
    return df

def create_mapping():
    """처리내용 매핑 규칙을 정의합니다."""
    # 1.xlsx의 처리내용 키워드와 매칭 규칙
    mapping_rules = {
        '가공 불가': 'XL',
        '오염부 리퍼 완료': '수리_세척',
        '정상 확인 및 클리닝 작업': '수리_세척',
        '구성품 완성': '수리_기타',
        '수리 또는 부품 교체': '수리_부품교체',
        '전원부 수리 또는 교체': '수리_부품교체',
        'S/W 재설정': '수리_SW'
    }
    
    return mapping_rules

def match_processing_content(처리내용, df1_data, mapping_rules):
    """처리내용을 1.xlsx 데이터와 매칭하여 수리결과를 찾습니다."""
    
    # 직접 매칭 시도
    for _, row in df1_data.iterrows():
        if 처리내용 in row['처리내용'] or row['처리내용'] in 처리내용:
            return row['수리결과']
    
    # 키워드 기반 매칭
    for keyword, result in mapping_rules.items():
        if keyword in 처리내용:
            return result
    
    # 매칭되지 않은 경우
    return '매칭 항목 없음'

def create_result_file(df1, df2):
    """매칭 결과 파일을 생성합니다."""
    mapping_rules = create_mapping()
    
    print("\n=== 매칭 과정 ===")
    
    # 결과 데이터 리스트
    result_data = []
    
    # 매칭 통계
    매칭_성공 = 0
    매칭_실패 = 0
    
    # 2.xlsx의 각 항목에 대해 매칭 수행
    for idx, row in df2.iterrows():
        qaid = row['QAID']
        처리내용 = row['처리내용']
        
        # 수리결과 매칭
        수리결과 = match_processing_content(처리내용, df1, mapping_rules)
        
        if 수리결과 != '매칭 항목 없음':
            매칭_성공 += 1
            if idx < 10:  # 처음 10개만 출력
                print(f"매칭 성공: '{처리내용}' -> '{수리결과}'")
        else:
            매칭_실패 += 1
            if idx < 10:  # 처음 10개만 출력
                print(f"매칭 실패: '{처리내용}'")
        
        # 결과 데이터에 추가
        result_data.append({
            'QAID': qaid,
            '처리내용': 처리내용,
            '수리결과': 수리결과
        })
    
    # DataFrame으로 변환
    result_df = pd.DataFrame(result_data)
    
    print(f"\n=== 매칭 통계 ===")
    print(f"총 항목 수: {len(result_df)}")
    print(f"매칭 성공: {매칭_성공}개")
    print(f"매칭 실패: {매칭_실패}개")
    print(f"매칭 성공률: {매칭_성공/len(result_df)*100:.1f}%")
    
    return result_df

def main():
    """메인 실행 함수"""
    print("Excel 파일 매칭 프로그램 (올바른 버전)")
    print("QAID, 처리내용, 수리결과 매칭")
    
    # 파일 로드
    df1 = load_file1()
    df2 = load_file2()
    
    # 매칭 및 결과 생성
    result_df = create_result_file(df1, df2)
    
    # 결과 미리보기
    print("\n=== 결과 미리보기 (처음 10개) ===")
    print(result_df.head(10))
    
    # 수리결과별 통계
    print(f"\n=== 수리결과별 통계 ===")
    수리결과_통계 = result_df['수리결과'].value_counts()
    print(수리결과_통계)
    
    # 결과 파일 저장
    output_filename = "QAID_처리내용_수리결과_매칭.xlsx"
    result_df.to_excel(output_filename, index=False)
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    
    # 매칭되지 않은 항목들 확인
    unmatched = result_df[result_df['수리결과'] == '매칭 항목 없음']
    if not unmatched.empty:
        print(f"\n=== 매칭되지 않은 처리내용 (처음 10개) ===")
        unique_unmatched = unmatched['처리내용'].unique()[:10]
        for item in unique_unmatched:
            print(f"  - {item}")

if __name__ == "__main__":
    main()
