import pandas as pd
import numpy as np

def load_file1():
    """1.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("1.xlsx", header=None)
    
    # 첫 번째 행에 헤더가 있으므로 이를 제거하고 컬럼명 설정
    df = df.iloc[1:].reset_index(drop=True)  # 첫 번째 행(헤더) 제거
    df.columns = ['빈컬럼', '처리내용', '수리결과']
    
    # 빈 컬럼 제거
    df = df[['처리내용', '수리결과']].copy()
    
    # 헤더 행 제거 (처리내용이 '처리내용'인 행)
    df = df[df['처리내용'] != '처리내용'].reset_index(drop=True)
    
    # NaN 값 제거
    df = df.dropna().reset_index(drop=True)
    
    print("=== 1.xlsx 데이터 ===")
    print(df)
    print(f"총 {len(df)}개 항목")
    
    return df

def load_file2():
    """2.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("2.xlsx", header=None)

    print("\n=== 2.xlsx 원본 데이터 구조 확인 ===")
    print(f"전체 shape: {df.shape}")
    print("첫 10행:")
    print(df.head(10))

    # 데이터 구조 파악 후 필요한 부분만 추출
    # 행 레이블이 있는 부분부터 시작 (row 2부터)
    df_clean = df.iloc[2:].reset_index(drop=True)

    print(f"\n추출된 데이터 shape: {df_clean.shape}")
    print("추출된 데이터:")
    print(df_clean)

    # 실제 컬럼 수에 맞게 컬럼명 설정
    num_cols = df_clean.shape[1]
    if num_cols >= 5:
        df_clean = df_clean.iloc[:, :5]  # 처음 5개 컬럼만 사용
        df_clean.columns = ['처리내용', '수량', '수리비용', '별도수리비', '추가비용']
    else:
        # 컬럼 수가 부족한 경우 기본 컬럼명 사용
        df_clean.columns = [f'컬럼_{i}' for i in range(num_cols)]

    # 헤더 행 제거 ('행 레이블', '합계 : 수량' 등이 포함된 행)
    if '처리내용' in df_clean.columns:
        df_clean = df_clean[df_clean['처리내용'] != '행 레이블'].reset_index(drop=True)
        df_clean = df_clean[df_clean['처리내용'] != '총합계'].reset_index(drop=True)
        df_clean = df_clean.dropna(subset=['처리내용']).reset_index(drop=True)

    print("\n=== 2.xlsx 최종 데이터 ===")
    print(df_clean)
    print(f"총 {len(df_clean)}개 항목")

    return df_clean

def create_mapping():
    """처리내용 매핑 규칙을 정의합니다."""
    # 1.xlsx의 수리결과 카테고리와 2.xlsx의 처리내용 카테고리 매핑
    mapping = {
        'XL': ['가공 불가'],
        '리퍼완료': ['오염부 리퍼 완료', '정상 확인 및 클리닝 작업'],
        '점검완료(중)': ['구성품 완성', '수리 또는 부품 교체', '전원부 수리 또는 교체', 'S/W 재설정']
    }
    
    # 역방향 매핑도 생성 (1.xlsx 처리내용 -> 2.xlsx 카테고리)
    reverse_mapping = {}
    for category, items in mapping.items():
        for item in items:
            reverse_mapping[item] = category
    
    return mapping, reverse_mapping

def match_and_create_result(df1, df2):
    """2.xlsx를 기준으로 매칭하여 수리결과를 추가합니다."""
    mapping, reverse_mapping = create_mapping()
    
    # 2.xlsx를 복사하여 결과 데이터프레임 생성
    result_df = df2.copy()
    
    # 매칭된 수리결과들을 저장할 리스트
    matched_results = []
    
    print("\n=== 매칭 과정 ===")
    
    # 2.xlsx의 각 카테고리에 대해 처리
    for idx, row in result_df.iterrows():
        카테고리 = row['처리내용']
        
        print(f"\n--- 2.xlsx 카테고리: '{카테고리}' 처리 중 ---")
        
        # 해당 카테고리와 매칭되는 1.xlsx의 수리결과들 찾기
        category_results = []
        
        if 카테고리 in mapping:
            keywords = mapping[카테고리]
            for keyword in keywords:
                matching_rows = df1[df1['처리내용'].str.contains(keyword, na=False)]
                for _, row1 in matching_rows.iterrows():
                    if row1['수리결과'] not in category_results:
                        category_results.append(row1['수리결과'])
                    print(f"  매칭됨: '{row1['처리내용']}' -> 수리결과: '{row1['수리결과']}'")
        
        # 매칭된 수리결과들을 문자열로 결합
        if category_results:
            결합된_수리결과 = ', '.join(category_results)
            matched_results.append(결합된_수리결과)
            print(f"  최종 수리결과: {결합된_수리결과}")
        else:
            matched_results.append('매칭 항목 없음')
            print(f"  매칭 안됨: '{카테고리}' (1.xlsx에 해당 항목 없음)")
    
    # 수리결과 컬럼을 처리내용 옆에 추가
    result_df.insert(1, '수리결과', matched_results)
    
    return result_df

def main():
    """메인 실행 함수"""
    print("Excel 파일 매칭 프로그램 v2 시작")
    print("2.xlsx 기준으로 처리내용 옆에 수리결과 컬럼 추가")
    
    # 파일 로드
    df1 = load_file1()
    df2 = load_file2()
    
    # 매칭 및 결과 생성
    result_df = match_and_create_result(df1, df2)
    
    # 결과 출력
    print("\n=== 최종 결과 ===")
    print(result_df)
    
    # 결과 파일 저장
    output_filename = "매칭_결과_v2.xlsx"
    result_df.to_excel(output_filename, index=False)
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    
    # 요약 통계
    print(f"\n=== 요약 ===")
    print(f"2.xlsx 총 카테고리 수: {len(result_df)}")
    print(f"매칭 성공 카테고리: {len(result_df[result_df['수리결과'] != '매칭 항목 없음'])}")
    print(f"매칭 실패 카테고리: {len(result_df[result_df['수리결과'] == '매칭 항목 없음'])}")
    
    # 각 카테고리별 매칭 결과 요약
    print(f"\n=== 카테고리별 매칭 결과 ===")
    for idx, row in result_df.iterrows():
        print(f"'{row['처리내용']}' -> 수리결과: '{row['수리결과']}'")

if __name__ == "__main__":
    main()
