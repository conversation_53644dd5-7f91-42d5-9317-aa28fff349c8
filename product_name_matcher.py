import pandas as pd
import numpy as np
from difflib import SequenceMatcher
import re

def load_4xlsx_product_data():
    """4.xlsx 파일에서 제품명 관련 데이터를 로드합니다."""
    print("=== 4.xlsx 파일 로드 ===")
    
    # 첫 번째 헤더 (행 1)로 로드
    df = pd.read_excel("4.xlsx", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['QAID', '제품명', '상품단가구간', '수리결과']
    df_selected = df[required_columns].copy()
    
    # NaN 값 제거
    df_selected = df_selected.dropna().reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"컬럼: {list(df_selected.columns)}")
    print("데이터 샘플:")
    print(df_selected.head())
    
    # 데이터 통계
    print(f"\n=== 4.xlsx 데이터 통계 ===")
    print(f"제품명 고유값: {df_selected['제품명'].nunique()}개")
    print(f"상품단가구간: {df_selected['상품단가구간'].unique()}")
    print(f"수리결과: {df_selected['수리결과'].unique()}")
    
    return df_selected

def load_price_monitor_data():
    """price_new.xlsx의 모니터사이즈(미기재품목) 시트를 로드합니다."""
    print("\n=== price_new.xlsx 모니터사이즈(미기재품목) 시트 로드 ===")
    
    # 헤더 행 1로 로드
    df = pd.read_excel("price_new.xlsx", sheet_name="모니터사이즈(미기재품목)", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['제품명', '수리_부품교체', '수리_세척', '수리_기타', '검수']
    df_selected = df[required_columns].copy()
    
    # NaN 값이 있는 행 제거
    df_selected = df_selected.dropna(subset=['제품명']).reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"컬럼: {list(df_selected.columns)}")
    print("데이터 샘플:")
    print(df_selected.head())
    
    print(f"\n=== price_new.xlsx 데이터 통계 ===")
    print(f"제품명 고유값: {df_selected['제품명'].nunique()}개")
    
    return df_selected

def clean_product_name(product_name):
    """제품명을 정리하여 매칭 정확도를 높입니다."""
    if pd.isna(product_name):
        return ""
    
    # 문자열로 변환
    name = str(product_name)
    
    # 특수 문자 및 불필요한 텍스트 제거
    name = re.sub(r'\[.*?\]', '', name)  # 대괄호 내용 제거
    name = re.sub(r'\(.*?\)', '', name)  # 소괄호 내용 제거
    name = re.sub(r'[^\w\s가-힣]', ' ', name)  # 특수문자 제거
    name = re.sub(r'\s+', ' ', name)  # 연속 공백 제거
    name = name.strip().lower()  # 앞뒤 공백 제거 및 소문자 변환
    
    return name

def calculate_similarity(str1, str2):
    """두 문자열 간의 유사도를 계산합니다."""
    return SequenceMatcher(None, str1, str2).ratio()

def find_best_match(target_product, price_products, threshold=0.6):
    """가장 유사한 제품명을 찾습니다."""
    target_clean = clean_product_name(target_product)
    
    best_match = None
    best_score = 0
    
    for price_product in price_products:
        price_clean = clean_product_name(price_product)
        
        # 유사도 계산
        similarity = calculate_similarity(target_clean, price_clean)
        
        # 부분 문자열 매칭도 확인
        if target_clean in price_clean or price_clean in target_clean:
            similarity = max(similarity, 0.8)
        
        if similarity > best_score and similarity >= threshold:
            best_score = similarity
            best_match = price_product
    
    return best_match, best_score

def perform_product_matching(df_4xlsx, df_price):
    """제품명 기반 매칭 및 유효성 검증을 수행합니다."""
    print("\n=== 제품명 기반 매칭 및 유효성 검증 수행 ===")
    
    # price_new.xlsx의 제품명 리스트
    price_products = df_price['제품명'].tolist()
    
    # price_new.xlsx에서 제품명과 가격 정보 딕셔너리 생성
    price_dict = {}
    for _, row in df_price.iterrows():
        price_dict[row['제품명']] = {
            '수리_부품교체': row['수리_부품교체'],
            '수리_세척': row['수리_세척'],
            '수리_기타': row['수리_기타'],
            '검수': row['검수']
        }
    
    print(f"price_new.xlsx 제품 수: {len(price_products)}개")
    
    # 결과 데이터 리스트
    result_data = []
    
    # 매칭 통계
    매칭_성공 = 0
    매칭_실패 = 0
    유효성_통과 = 0
    유효성_실패 = 0
    
    # 4.xlsx의 각 항목에 대해 매칭 수행
    for idx, row in df_4xlsx.iterrows():
        qaid = row['QAID']
        제품명 = row['제품명']
        상품단가구간 = row['상품단가구간']
        수리결과 = row['수리결과']
        
        # 제품명 매칭 시도
        matched_product, similarity_score = find_best_match(제품명, price_products)
        
        if matched_product:
            매칭_성공 += 1
            매칭상태 = "매칭됨"
            
            # 해당 제품의 가격 정보 가져오기
            price_info = price_dict[matched_product]
            
            # 수리결과에 해당하는 가격 정보 확인
            if 수리결과 in price_info:
                예상가격 = price_info[수리결과]
                유효성_통과 += 1
                유효성검증 = "통과"
            else:
                예상가격 = "해당없음"
                유효성_실패 += 1
                유효성검증 = "실패 - 수리결과 불일치"
            
            result_data.append({
                'QAID': qaid,
                '제품명': 제품명,
                '매칭된_제품명': matched_product,
                '유사도': round(similarity_score, 3),
                '상품단가구간': 상품단가구간,
                '수리결과': 수리결과,
                '매칭상태': 매칭상태,
                '예상가격': 예상가격,
                '수리_부품교체': price_info['수리_부품교체'],
                '수리_세척': price_info['수리_세척'],
                '수리_기타': price_info['수리_기타'],
                '검수': price_info['검수'],
                '유효성검증': 유효성검증
            })
        else:
            매칭_실패 += 1
            유효성_실패 += 1
            
            result_data.append({
                'QAID': qaid,
                '제품명': 제품명,
                '매칭된_제품명': "매칭실패",
                '유사도': 0.0,
                '상품단가구간': 상품단가구간,
                '수리결과': 수리결과,
                '매칭상태': "별도항목 - 매칭실패",
                '예상가격': "매칭실패",
                '수리_부품교체': "매칭실패",
                '수리_세척': "매칭실패",
                '수리_기타': "매칭실패",
                '검수': "매칭실패",
                '유효성검증': "실패 - 매칭불가"
            })
        
        # 진행상황 출력 (처음 10개)
        if idx < 10:
            status = "매칭됨" if matched_product else "별도항목 - 매칭실패"
            score = similarity_score if matched_product else 0.0
            print(f"처리 {idx+1}: {제품명[:50]}... -> {status} (유사도: {score:.3f})")
    
    # DataFrame으로 변환
    result_df = pd.DataFrame(result_data)
    
    # 통계 출력
    print(f"\n=== 매칭 및 검증 통계 ===")
    print(f"총 처리 항목: {len(result_df)}")
    print(f"매칭 성공: {매칭_성공}개 ({매칭_성공/len(result_df)*100:.1f}%)")
    print(f"매칭 실패: {매칭_실패}개 ({매칭_실패/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 통과: {유효성_통과}개 ({유효성_통과/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 실패: {유효성_실패}개 ({유효성_실패/len(result_df)*100:.1f}%)")
    
    return result_df

def analyze_results(result_df):
    """결과 분석을 수행합니다."""
    print(f"\n=== 결과 분석 ===")
    
    # 매칭 상태별 통계
    print("매칭 상태별 통계:")
    매칭상태_통계 = result_df['매칭상태'].value_counts()
    for 상태, 개수 in 매칭상태_통계.items():
        print(f"  {상태}: {개수}개")
    
    # 유효성 검증 결과별 통계
    print("\n유효성 검증 결과별 통계:")
    유효성검증_통계 = result_df['유효성검증'].value_counts()
    for 결과, 개수 in 유효성검증_통계.items():
        print(f"  {결과}: {개수}개")
    
    # 수리결과별 매칭 성공률
    print("\n수리결과별 매칭 성공률:")
    수리결과별_통계 = result_df.groupby('수리결과').agg({
        '매칭상태': lambda x: (x == '매칭됨').sum(),
        'QAID': 'count'
    }).rename(columns={'매칭상태': '매칭성공', 'QAID': '총개수'})
    수리결과별_통계['성공률'] = (수리결과별_통계['매칭성공'] / 수리결과별_통계['총개수'] * 100).round(1)
    print(수리결과별_통계)
    
    # 유사도 분포
    매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
    if not 매칭성공.empty:
        print(f"\n유사도 분포 (매칭 성공 항목):")
        print(f"  평균 유사도: {매칭성공['유사도'].mean():.3f}")
        print(f"  최고 유사도: {매칭성공['유사도'].max():.3f}")
        print(f"  최저 유사도: {매칭성공['유사도'].min():.3f}")

def main():
    """메인 실행 함수"""
    print("4.xlsx와 price_new.xlsx 제품명 매칭 및 유효성 검증 프로그램")
    print("=" * 70)
    
    # 파일 로드
    df_4xlsx = load_4xlsx_product_data()
    df_price = load_price_monitor_data()
    
    # 매칭 및 유효성 검증 수행
    result_df = perform_product_matching(df_4xlsx, df_price)
    
    # 결과 분석
    analyze_results(result_df)
    
    # 결과 미리보기
    print(f"\n=== 결과 미리보기 (처음 10개) ===")
    display_columns = ['QAID', '제품명', '매칭된_제품명', '유사도', '수리결과', '예상가격', '매칭상태']
    print(result_df[display_columns].head(10))
    
    # 별도항목 (매칭 실패) 미리보기
    별도항목 = result_df[result_df['매칭상태'].str.contains('별도항목')]
    if not 별도항목.empty:
        print(f"\n=== 별도항목 (매칭 실패) 미리보기 (처음 5개) ===")
        print(별도항목[['QAID', '제품명', '수리결과', '매칭상태']].head())
    
    # 결과 파일 저장
    output_filename = "제품명_매칭_유효성검증_결과.xlsx"
    
    # 여러 시트로 저장
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        # 전체 결과
        result_df.to_excel(writer, sheet_name='전체결과', index=False)
        
        # 매칭 성공 항목만
        매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
        매칭성공.to_excel(writer, sheet_name='매칭성공', index=False)
        
        # 별도항목 (매칭 실패)
        if not 별도항목.empty:
            별도항목.to_excel(writer, sheet_name='별도항목_매칭실패', index=False)
        
        # 유효성 검증 실패 항목
        유효성실패 = result_df[result_df['유효성검증'].str.contains('실패')]
        if not 유효성실패.empty:
            유효성실패.to_excel(writer, sheet_name='유효성검증실패', index=False)
    
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    print("시트 구성: 전체결과, 매칭성공, 별도항목_매칭실패, 유효성검증실패")

if __name__ == "__main__":
    main()
