import pandas as pd
import openpyxl

def analyze_excel_file(filename):
    """Excel 파일의 구조를 분석합니다."""
    print(f"\n=== {filename} 분석 ===")

    try:
        # 여러 방법으로 파일 읽기 시도
        print("방법 1: 기본 읽기")
        df = pd.read_excel(filename)
        print(f"행 수: {len(df)}")
        print(f"열 수: {len(df.columns)}")
        print(f"컬럼명: {list(df.columns)}")
        print("전체 데이터:")
        print(df)

        print("\n방법 2: header=None으로 읽기")
        df_no_header = pd.read_excel(filename, header=None)
        print("전체 데이터 (header=None):")
        print(df_no_header)

        print("\n방법 3: header=0으로 읽기")
        df_header0 = pd.read_excel(filename, header=0)
        print("전체 데이터 (header=0):")
        print(df_header0)

        # 처리내용과 수리결과 컬럼 찾기
        for i, df_test in enumerate([df, df_no_header, df_header0]):
            print(f"\n데이터프레임 {i+1}에서 '처리내용', '수리결과' 검색:")
            for col in df_test.columns:
                col_values = df_test[col].astype(str).tolist()
                if any('처리내용' in str(val) for val in col_values):
                    print(f"  '처리내용' 발견 - 컬럼: {col}")
                if any('수리결과' in str(val) for val in col_values):
                    print(f"  '수리결과' 발견 - 컬럼: {col}")

        return df

    except Exception as e:
        print(f"파일 읽기 오류: {e}")
        return None

if __name__ == "__main__":
    # 두 파일 분석
    df1 = analyze_excel_file("1.xlsx")
    df2 = analyze_excel_file("2.xlsx")
