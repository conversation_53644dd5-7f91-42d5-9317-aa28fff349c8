import pandas as pd
import openpyxl

def analyze_file_structure(filename):
    """파일 구조를 분석합니다."""
    print(f"\n=== {filename} 파일 분석 ===")
    
    try:
        # 시트 목록 확인
        wb = openpyxl.load_workbook(filename)
        print(f"시트 목록: {wb.sheetnames}")
        
        # 첫 번째 시트 분석
        first_sheet = wb.sheetnames[0]
        df = pd.read_excel(filename, sheet_name=first_sheet, header=None)
        print(f"Shape: {df.shape}")
        
        # 첫 10행 확인하여 헤더 위치 찾기
        print("첫 10행 데이터:")
        for i in range(min(10, len(df))):
            row_data = []
            for j in range(min(10, len(df.columns))):
                cell_value = str(df.iloc[i, j])[:20]
                row_data.append(f"[{j}]{cell_value}")
            print(f"행 {i}: {' | '.join(row_data)}")
        
        # 제품명 키워드 검색
        keywords = ['제품명', 'bar_cd', '구간', '수리_부품교체', '수리_세척', '수리_기타', '검수']
        print(f"\n키워드 검색:")
        
        for keyword in keywords:
            for i in range(min(10, len(df))):
                for j in range(len(df.columns)):
                    cell_value = str(df.iloc[i, j])
                    if keyword == cell_value:
                        print(f"  '{keyword}' 발견: 행 {i+1}, 열 {j+1}")
        
        # 헤더 행 테스트
        print(f"\n헤더 행 테스트:")
        for header_row in range(3):
            try:
                df_test = pd.read_excel(filename, sheet_name=first_sheet, header=header_row)
                print(f"  헤더 행 {header_row}: {list(df_test.columns)[:10]}")
                if '제품명' in df_test.columns:
                    print(f"    -> 제품명 컬럼 발견!")
                    print(f"    -> 데이터 샘플:")
                    print(df_test.head(3))
                    return df_test, header_row
            except:
                continue
        
        wb.close()
        return None, None
        
    except Exception as e:
        print(f"{filename} 분석 오류: {e}")
        return None, None

def check_files_exist():
    """a.xlsx와 b.xlsx 파일이 존재하는지 확인합니다."""
    import os
    
    files_to_check = ['a.xlsx', 'b.xlsx']
    existing_files = []
    
    for file in files_to_check:
        if os.path.exists(file):
            existing_files.append(file)
            print(f"✓ {file} 파일 존재")
        else:
            print(f"✗ {file} 파일 없음")
    
    return existing_files

if __name__ == "__main__":
    print("a.xlsx와 b.xlsx 파일 구조 분석")
    print("=" * 50)
    
    # 파일 존재 확인
    existing_files = check_files_exist()
    
    if len(existing_files) < 2:
        print("\n경고: a.xlsx 또는 b.xlsx 파일이 없습니다.")
        print("현재 디렉토리의 파일 목록:")
        import os
        files = [f for f in os.listdir('.') if f.endswith('.xlsx')]
        for file in files:
            print(f"  - {file}")
    else:
        # 각 파일 분석
        for filename in existing_files:
            df, header_row = analyze_file_structure(filename)
            if df is not None:
                print(f"\n{filename} 로드 성공 (헤더 행: {header_row})")
            else:
                print(f"\n{filename} 로드 실패")
