import pandas as pd
import numpy as np

def load_file1():
    """1.xlsx 파일을 로드하고 정리합니다."""
    df = pd.read_excel("1.xlsx", header=None)
    
    # 첫 번째 행에 헤더가 있으므로 이를 제거하고 컬럼명 설정
    df = df.iloc[1:].reset_index(drop=True)  # 첫 번째 행(헤더) 제거
    df.columns = ['빈컬럼', '처리내용', '수리결과']
    
    # 빈 컬럼 제거
    df = df[['처리내용', '수리결과']].copy()
    
    # 헤더 행 제거 (처리내용이 '처리내용'인 행)
    df = df[df['처리내용'] != '처리내용'].reset_index(drop=True)
    
    # NaN 값 제거
    df = df.dropna().reset_index(drop=True)
    
    print("=== 1.xlsx 데이터 ===")
    print(df)
    print(f"총 {len(df)}개 항목")
    
    return df

def load_file2():
    """2.xlsx 파일에서 처리내용 카테고리 데이터를 로드합니다."""
    df = pd.read_excel("2.xlsx", header=None)

    print(f"2.xlsx 파일 shape: {df.shape}")

    # 실제 컬럼 수에 맞게 조정
    if df.shape[1] < 15:
        print(f"경고: 컬럼 수가 {df.shape[1]}개로 15개 미만입니다.")
        # 사용 가능한 컬럼에서 카테고리 찾기
        category_column_idx = None
        for col_idx in range(df.shape[1]):
            col_values = df.iloc[:, col_idx].astype(str)
            if any(val in ['XL', '리퍼완료', '점검완료(중)', '점검완료'] for val in col_values):
                category_column_idx = col_idx
                print(f"카테고리 컬럼 발견: {col_idx+1}번째 컬럼")
                break
    else:
        category_column_idx = 14  # 15번째 컬럼 (0-based index 14)

    if category_column_idx is None:
        print("카테고리 컬럼을 찾을 수 없습니다. 기본 데이터를 생성합니다.")
        # 기본 카테고리 데이터 생성
        category_data = [
            {'처리내용': 'XL', '수량': 0, '수리비용': 0, '별도수리비': 0, '추가비용': 0},
            {'처리내용': '리퍼완료', '수량': 0, '수리비용': 0, '별도수리비': 0, '추가비용': 0},
            {'처리내용': '점검완료(중)', '수량': 0, '수리비용': 0, '별도수리비': 0, '추가비용': 0}
        ]
    else:
        # 유효한 카테고리들만 필터링
        valid_categories = ['XL', '리퍼완료', '점검완료(중)', '점검완료']

        # 카테고리별 데이터 수집
        category_data = []

        for category in valid_categories:
            # 해당 카테고리가 있는 행들 찾기
            category_rows = df[df.iloc[:, category_column_idx] == category]

            if not category_rows.empty:
                # 각 카테고리별로 대표 데이터 생성 (첫 번째 행 기준)
                category_info = {
                    '처리내용': category,
                    '수량': len(category_rows),  # 해당 카테고리의 항목 수
                    '수리비용': 0,  # 실제 데이터에서 추출 필요
                    '별도수리비': 0,
                    '추가비용': 0
                }
                category_data.append(category_info)

                print(f"카테고리 '{category}': {len(category_rows)}개 항목")

    # DataFrame으로 변환
    df_categories = pd.DataFrame(category_data)

    print("\n=== 2.xlsx 카테고리 데이터 ===")
    print(df_categories)
    print(f"총 {len(df_categories)}개 카테고리")

    return df_categories

def create_mapping():
    """처리내용 매핑 규칙을 정의합니다."""
    # 1.xlsx의 처리내용과 2.xlsx의 카테고리 매핑
    mapping = {
        'XL': ['가공 불가'],
        '리퍼완료': ['오염부 리퍼 완료', '정상 확인 및 클리닝 작업'],
        '점검완료(중)': ['구성품 완성', '수리 또는 부품 교체', '전원부 수리 또는 교체', 'S/W 재설정'],
        '점검완료': ['구성품 완성', '수리 또는 부품 교체', '전원부 수리 또는 교체', 'S/W 재설정']
    }
    
    return mapping

def match_and_create_result(df1, df2):
    """2.xlsx를 기준으로 매칭하여 수리결과를 추가합니다."""
    mapping = create_mapping()
    
    # 2.xlsx를 복사하여 결과 데이터프레임 생성
    result_df = df2.copy()
    
    # 매칭된 수리결과들을 저장할 리스트
    matched_results = []
    
    print("\n=== 매칭 과정 ===")
    
    # 2.xlsx의 각 카테고리에 대해 처리
    for idx, row in result_df.iterrows():
        카테고리 = row['처리내용']
        
        print(f"\n--- 2.xlsx 카테고리: '{카테고리}' 처리 중 ---")
        
        # 해당 카테고리와 매칭되는 1.xlsx의 수리결과들 찾기
        category_results = []
        
        if 카테고리 in mapping:
            keywords = mapping[카테고리]
            for keyword in keywords:
                matching_rows = df1[df1['처리내용'].str.contains(keyword, na=False)]
                for _, row1 in matching_rows.iterrows():
                    if row1['수리결과'] not in category_results:
                        category_results.append(row1['수리결과'])
                    print(f"  매칭됨: '{row1['처리내용']}' -> 수리결과: '{row1['수리결과']}'")
        
        # 매칭된 수리결과들을 문자열로 결합
        if category_results:
            결합된_수리결과 = ', '.join(category_results)
            matched_results.append(결합된_수리결과)
            print(f"  최종 수리결과: {결합된_수리결과}")
        else:
            matched_results.append('매칭 항목 없음')
            print(f"  매칭 안됨: '{카테고리}' (1.xlsx에 해당 항목 없음)")
    
    # 수리결과 컬럼을 처리내용 옆에 추가
    result_df.insert(1, '수리결과', matched_results)
    
    return result_df

def main():
    """메인 실행 함수"""
    print("Excel 파일 매칭 프로그램 (최종 버전)")
    print("2.xlsx 기준으로 처리내용 옆에 수리결과 컬럼 추가")
    
    # 파일 로드
    df1 = load_file1()
    df2 = load_file2()
    
    # 매칭 및 결과 생성
    result_df = match_and_create_result(df1, df2)
    
    # 결과 출력
    print("\n=== 최종 결과 ===")
    print(result_df)
    
    # 결과 파일 저장
    output_filename = "매칭_결과_최종.xlsx"
    result_df.to_excel(output_filename, index=False)
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    
    # 요약 통계
    print(f"\n=== 요약 ===")
    print(f"2.xlsx 총 카테고리 수: {len(result_df)}")
    print(f"매칭 성공 카테고리: {len(result_df[result_df['수리결과'] != '매칭 항목 없음'])}")
    print(f"매칭 실패 카테고리: {len(result_df[result_df['수리결과'] == '매칭 항목 없음'])}")
    
    # 각 카테고리별 매칭 결과 요약
    print(f"\n=== 카테고리별 매칭 결과 ===")
    for idx, row in result_df.iterrows():
        print(f"'{row['처리내용']}' -> 수리결과: '{row['수리결과']}'")

if __name__ == "__main__":
    main()
