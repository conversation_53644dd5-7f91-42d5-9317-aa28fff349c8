import pandas as pd
import numpy as np

def load_4xlsx_product_data():
    """4.xlsx 파일에서 제품명 관련 데이터를 로드합니다."""
    print("=== 4.xlsx 파일 로드 ===")

    # 첫 번째 헤더 (행 1)로 로드
    df = pd.read_excel("4.xlsx", header=1)

    # 필요한 컬럼만 선택 (4차, 5차 컬럼 추가)
    required_columns = ['QAID', '4차', '5차', '제품명', '상품단가구간', '수리결과']
    df_selected = df[required_columns].copy()

    # NaN 값 제거
    df_selected = df_selected.dropna().reset_index(drop=True)

    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"제품명 고유값: {df_selected['제품명'].nunique()}개")
    print(f"4차 카테고리: {df_selected['4차'].nunique()}개")
    print(f"5차 카테고리: {df_selected['5차'].nunique()}개")
    print("데이터 샘플:")
    print(df_selected.head())

    return df_selected

def load_price_data():
    """price_new.xlsx에서 가격 데이터를 로드합니다."""
    print("\n=== price_new.xlsx 가격 데이터 로드 ===")

    # 먼저 데이터유효성_일반 시트에서 4차, 5차, 상품단가구간별 가격 정보 로드
    df_general = pd.read_excel("price_new.xlsx", sheet_name="데이터유효성_일반", header=2)

    # 필요한 컬럼만 선택
    general_columns = ['4차', '5차', '상품단가구간', '수리_부품교체', '수리_세척', '수리_SW', '수리_기타']
    df_general_selected = df_general[general_columns].copy()

    # NaN 값이 있는 행 제거
    df_general_selected = df_general_selected.dropna(subset=['4차', '5차', '상품단가구간']).reset_index(drop=True)

    print(f"데이터유효성_일반 로드된 데이터: {len(df_general_selected)}개")
    print("데이터유효성_일반 샘플:")
    print(df_general_selected.head())

    # 모니터사이즈(미기재품목) 시트에서 제품명별 가격 정보도 로드
    df_monitor = pd.read_excel("price_new.xlsx", sheet_name="모니터사이즈(미기재품목)", header=1)
    monitor_columns = ['제품명', '수리_부품교체', '수리_세척', '수리_기타', '검수']
    df_monitor_selected = df_monitor[monitor_columns].copy()
    df_monitor_selected = df_monitor_selected.dropna(subset=['제품명']).reset_index(drop=True)

    print(f"모니터사이즈 로드된 데이터: {len(df_monitor_selected)}개")

    return df_general_selected, df_monitor_selected

def normalize_product_name(product_name):
    """제품명을 정규화합니다 (공백 제거, 대소문자 통일)."""
    if pd.isna(product_name):
        return ""
    
    # 문자열로 변환하고 앞뒤 공백 제거
    normalized = str(product_name).strip()
    
    return normalized

def create_matching_dicts(df_general, df_monitor):
    """가격 데이터로 매칭 딕셔너리를 생성합니다."""
    print("매칭 딕셔너리 생성 중...")

    # 1. 4차, 5차, 상품단가구간 기반 매칭 딕셔너리
    category_match_dict = {}
    for idx, row in df_general.iterrows():
        key = f"{row['4차']}|{row['5차']}|{row['상품단가구간']}"
        category_match_dict[key] = {
            '수리_부품교체': row['수리_부품교체'],
            '수리_세척': row['수리_세척'],
            '수리_SW': row['수리_SW'],
            '수리_기타': row['수리_기타'],
            '검수': 0  # 데이터유효성_일반에는 검수 컬럼이 없음
        }

    # 2. 제품명 기반 매칭 딕셔너리
    product_match_dict = {}
    for idx, row in df_monitor.iterrows():
        제품명 = normalize_product_name(row['제품명'])
        if 제품명:
            product_match_dict[제품명] = {
                '제품명': row['제품명'],
                '수리_부품교체': row['수리_부품교체'],
                '수리_세척': row['수리_세척'],
                '수리_기타': row['수리_기타'],
                '검수': row['검수'],
                '수리_SW': 0  # 모니터사이즈에는 SW 컬럼이 없음
            }

    print(f"카테고리 매칭 딕셔너리: {len(category_match_dict)}개")
    print(f"제품명 매칭 딕셔너리: {len(product_match_dict)}개")

    return category_match_dict, product_match_dict

def perform_enhanced_matching(df_4xlsx, df_general, df_monitor):
    """향상된 매칭 및 유효성 검증을 수행합니다."""
    print("\n=== 향상된 매칭 및 유효성 검증 수행 ===")

    # 매칭 딕셔너리 생성
    category_match_dict, product_match_dict = create_matching_dicts(df_general, df_monitor)

    # 결과 데이터 리스트
    result_data = []

    # 매칭 통계
    카테고리_매칭_성공 = 0
    제품명_매칭_성공 = 0
    매칭_실패 = 0
    유효성_통과 = 0
    유효성_실패 = 0

    print("향상된 매칭 진행 중...")

    # 4.xlsx의 각 항목에 대해 매칭 수행
    for idx, row in df_4xlsx.iterrows():
        qaid = row['QAID']
        차4 = row['4차']
        차5 = row['5차']
        제품명 = row['제품명']
        상품단가구간 = row['상품단가구간']
        수리결과 = row['수리결과']

        # 1차 시도: 제품명 정확 매칭
        normalized_product_name = normalize_product_name(제품명)
        matched = False
        매칭방식 = ""
        price_info = {}

        if normalized_product_name in product_match_dict:
            제품명_매칭_성공 += 1
            matched = True
            매칭방식 = "제품명_정확일치"
            price_info = product_match_dict[normalized_product_name]
            matched_product_name = price_info['제품명']
        else:
            # 2차 시도: 4차, 5차, 상품단가구간 매칭
            category_key = f"{차4}|{차5}|{상품단가구간}"
            if category_key in category_match_dict:
                카테고리_매칭_성공 += 1
                matched = True
                매칭방식 = "카테고리_매칭"
                price_info = category_match_dict[category_key]
                matched_product_name = f"[카테고리매칭] {차4} > {차5} > {상품단가구간}"

        if matched:
            # 수리결과에 해당하는 가격 정보 확인
            if 수리결과 in price_info:
                예상가격 = price_info[수리결과]
                유효성_통과 += 1
                유효성검증 = "통과"
            else:
                예상가격 = "해당없음"
                유효성_실패 += 1
                유효성검증 = "실패 - 수리결과 불일치"

            result_data.append({
                'QAID': qaid,
                '4차': 차4,
                '5차': 차5,
                '제품명': 제품명,
                '매칭된_제품명': matched_product_name,
                '매칭방식': 매칭방식,
                '상품단가구간': 상품단가구간,
                '수리결과': 수리결과,
                '매칭상태': "매칭됨",
                '예상가격': 예상가격,
                '수리_부품교체': price_info.get('수리_부품교체', 0),
                '수리_세척': price_info.get('수리_세척', 0),
                '수리_SW': price_info.get('수리_SW', 0),
                '수리_기타': price_info.get('수리_기타', 0),
                '검수': price_info.get('검수', 0),
                '유효성검증': 유효성검증
            })
        else:
            매칭_실패 += 1
            유효성_실패 += 1

            result_data.append({
                'QAID': qaid,
                '4차': 차4,
                '5차': 차5,
                '제품명': 제품명,
                '매칭된_제품명': "매칭실패",
                '매칭방식': "매칭불가",
                '상품단가구간': 상품단가구간,
                '수리결과': 수리결과,
                '매칭상태': "별도항목 - 매칭실패",
                '예상가격': "매칭실패",
                '수리_부품교체': "매칭실패",
                '수리_세척': "매칭실패",
                '수리_기타': "매칭실패",
                '검수': "매칭실패",
                '유효성검증': "실패 - 매칭불가"
            })
        
        # 진행상황 출력
        if (idx + 1) % 5000 == 0:
            print(f"진행률: {idx + 1}/{len(df_4xlsx)} ({(idx + 1)/len(df_4xlsx)*100:.1f}%)")
    
    # DataFrame으로 변환
    result_df = pd.DataFrame(result_data)
    
    # 통계 출력
    print(f"\n=== 향상된 매칭 및 검증 통계 ===")
    print(f"총 처리 항목: {len(result_df)}")
    print(f"제품명 매칭 성공: {제품명_매칭_성공}개")
    print(f"카테고리 매칭 성공: {카테고리_매칭_성공}개")
    print(f"전체 매칭 성공: {제품명_매칭_성공 + 카테고리_매칭_성공}개 ({(제품명_매칭_성공 + 카테고리_매칭_성공)/len(result_df)*100:.1f}%)")
    print(f"매칭 실패: {매칭_실패}개 ({매칭_실패/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 통과: {유효성_통과}개 ({유효성_통과/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 실패: {유효성_실패}개 ({유효성_실패/len(result_df)*100:.1f}%)")
    
    return result_df

def analyze_results(result_df):
    """결과 분석을 수행합니다."""
    print(f"\n=== 결과 분석 ===")
    
    # 매칭 상태별 통계
    print("매칭 상태별 통계:")
    매칭상태_통계 = result_df['매칭상태'].value_counts()
    for 상태, 개수 in 매칭상태_통계.items():
        print(f"  {상태}: {개수}개")
    
    # 유효성 검증 결과별 통계
    print("\n유효성 검증 결과별 통계:")
    유효성검증_통계 = result_df['유효성검증'].value_counts()
    for 결과, 개수 in 유효성검증_통계.items():
        print(f"  {결과}: {개수}개")
    
    # 수리결과별 매칭 성공률
    print("\n수리결과별 매칭 성공률:")
    수리결과별_통계 = result_df.groupby('수리결과').agg({
        '매칭상태': lambda x: (x == '매칭됨').sum(),
        'QAID': 'count'
    }).rename(columns={'매칭상태': '매칭성공', 'QAID': '총개수'})
    수리결과별_통계['성공률'] = (수리결과별_통계['매칭성공'] / 수리결과별_통계['총개수'] * 100).round(1)
    print(수리결과별_통계)
    
    # 매칭 성공한 제품들의 상세 정보
    매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
    if not 매칭성공.empty:
        print(f"\n매칭 성공 제품 통계:")
        print(f"  총 매칭 성공 제품: {len(매칭성공)}개")
        print(f"  고유 제품명 수: {매칭성공['제품명'].nunique()}개")

def main():
    """메인 실행 함수"""
    print("4.xlsx와 price_new.xlsx 향상된 매칭 및 유효성 검증 프로그램")
    print("=" * 80)

    # 파일 로드
    df_4xlsx = load_4xlsx_product_data()
    df_general, df_monitor = load_price_data()

    # 향상된 매칭 및 유효성 검증 수행
    result_df = perform_enhanced_matching(df_4xlsx, df_general, df_monitor)
    
    # 결과 분석
    analyze_results(result_df)
    
    # 결과 미리보기
    print(f"\n=== 결과 미리보기 (처음 10개) ===")
    display_columns = ['QAID', '4차', '5차', '제품명', '매칭된_제품명', '매칭방식', '수리결과', '예상가격', '매칭상태']
    print(result_df[display_columns].head(10))

    # 매칭 성공 사례 미리보기
    매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
    if not 매칭성공.empty:
        print(f"\n=== 매칭 성공 사례 (처음 5개) ===")
        print(매칭성공[display_columns].head())
    else:
        print(f"\n매칭 성공 사례가 없습니다.")

    # 별도항목 (매칭 실패) 미리보기
    별도항목 = result_df[result_df['매칭상태'].str.contains('별도항목')]
    if not 별도항목.empty:
        print(f"\n=== 별도항목 (매칭 실패) 미리보기 (처음 5개) ===")
        print(별도항목[['QAID', '4차', '5차', '제품명', '수리결과', '매칭상태']].head())
    
    # 결과 파일 저장
    output_filename = "제품명_정확매칭_유효성검증_결과.xlsx"
    
    # 여러 시트로 저장
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        # 전체 결과
        result_df.to_excel(writer, sheet_name='전체결과', index=False)
        
        # 매칭 성공 항목만
        if not 매칭성공.empty:
            매칭성공.to_excel(writer, sheet_name='매칭성공', index=False)
        
        # 별도항목 (매칭 실패)
        if not 별도항목.empty:
            별도항목.to_excel(writer, sheet_name='별도항목_매칭실패', index=False)
        
        # 유효성 검증 실패 항목
        유효성실패 = result_df[result_df['유효성검증'].str.contains('실패')]
        if not 유효성실패.empty:
            유효성실패.to_excel(writer, sheet_name='유효성검증실패', index=False)
    
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    print("시트 구성: 전체결과, 매칭성공, 별도항목_매칭실패, 유효성검증실패")

if __name__ == "__main__":
    main()
