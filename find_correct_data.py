import pandas as pd
import openpyxl

def explore_excel_file(filename):
    """Excel 파일의 모든 시트와 구조를 탐색합니다."""
    print(f"\n=== {filename} 전체 탐색 ===")
    
    try:
        # openpyxl로 시트 이름 확인
        wb = openpyxl.load_workbook(filename)
        print(f"시트 이름들: {wb.sheetnames}")
        
        # 각 시트별로 확인
        for sheet_name in wb.sheetnames:
            print(f"\n--- 시트: {sheet_name} ---")
            df = pd.read_excel(filename, sheet_name=sheet_name, header=None)
            print(f"Shape: {df.shape}")
            
            # 처리내용이나 수리결과가 포함된 영역 찾기
            for i in range(min(20, len(df))):
                for j in range(min(10, len(df.columns))):
                    cell_value = str(df.iloc[i, j])
                    if '처리내용' in cell_value or '수리결과' in cell_value or 'XL' in cell_value or '리퍼완료' in cell_value or '점검완료' in cell_value:
                        print(f"  관련 데이터 발견 - 행 {i+1}, 열 {j+1}: {cell_value}")
                        
                        # 주변 데이터도 확인
                        print(f"  주변 데이터 (행 {max(0,i-2)}~{min(len(df),i+3)}):")
                        surrounding = df.iloc[max(0,i-2):min(len(df),i+3), max(0,j-2):min(len(df.columns),j+3)]
                        print(surrounding)
                        print()
                        
        wb.close()
        
    except Exception as e:
        print(f"파일 탐색 오류: {e}")

def search_for_categories():
    """XL, 리퍼완료, 점검완료(중) 등의 카테고리를 찾습니다."""
    print("\n=== 카테고리 검색 ===")
    
    # 2.xlsx에서 카테고리 검색
    try:
        df = pd.read_excel("2.xlsx", header=None)
        
        categories = ['XL', '리퍼완료', '점검완료', '점검완료(중)', '처리내용', '수리결과']
        
        for category in categories:
            found_positions = []
            for i in range(len(df)):
                for j in range(len(df.columns)):
                    cell_value = str(df.iloc[i, j])
                    if category in cell_value:
                        found_positions.append((i+1, j+1, cell_value))
            
            if found_positions:
                print(f"\n'{category}' 발견 위치:")
                for pos in found_positions[:5]:  # 처음 5개만 표시
                    print(f"  행 {pos[0]}, 열 {pos[1]}: {pos[2]}")
                    
    except Exception as e:
        print(f"카테고리 검색 오류: {e}")

if __name__ == "__main__":
    explore_excel_file("2.xlsx")
    search_for_categories()
