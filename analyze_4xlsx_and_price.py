import pandas as pd
import openpyxl

def analyze_4xlsx():
    """4.xlsx 파일의 구조를 분석합니다."""
    print("=== 4.xlsx 파일 분석 ===")
    
    try:
        # 시트 목록 확인
        wb = openpyxl.load_workbook("4.xlsx")
        print(f"시트 목록: {wb.sheetnames}")
        
        # 첫 번째 시트 분석
        first_sheet = wb.sheetnames[0]
        df = pd.read_excel("4.xlsx", sheet_name=first_sheet, header=None)
        print(f"Shape: {df.shape}")
        
        # 첫 10행 확인하여 헤더 위치 찾기
        print("첫 10행 데이터:")
        for i in range(min(10, len(df))):
            row_data = []
            for j in range(min(10, len(df.columns))):
                cell_value = str(df.iloc[i, j])[:15]
                row_data.append(f"[{j}]{cell_value}")
            print(f"행 {i}: {' | '.join(row_data)}")
        
        # QAID, 4차, 5차, 상품단가구간, 수리결과 키워드 검색
        keywords = ['QAID', '4차', '5차', '상품단가구간', '수리결과']
        print(f"\n키워드 검색:")
        
        for keyword in keywords:
            for i in range(min(10, len(df))):
                for j in range(len(df.columns)):
                    cell_value = str(df.iloc[i, j])
                    if keyword == cell_value:
                        print(f"  '{keyword}' 발견: 행 {i+1}, 열 {j+1}")
        
        wb.close()
        
    except Exception as e:
        print(f"4.xlsx 분석 오류: {e}")

def analyze_price_new():
    """price_new.xlsx 파일의 구조를 분석합니다."""
    print("\n=== price_new.xlsx 파일 분석 ===")
    
    try:
        # 시트 목록 확인
        wb = openpyxl.load_workbook("price_new.xlsx")
        print(f"시트 목록: {wb.sheetnames}")
        
        # 데이터유효성_일반 시트 분석
        if "데이터유효성_일반" in wb.sheetnames:
            print(f"\n--- 데이터유효성_일반 시트 분석 ---")
            df = pd.read_excel("price_new.xlsx", sheet_name="데이터유효성_일반", header=None)
            print(f"Shape: {df.shape}")
            
            # 첫 10행 확인
            print("첫 10행 데이터:")
            for i in range(min(10, len(df))):
                row_data = []
                for j in range(min(15, len(df.columns))):
                    cell_value = str(df.iloc[i, j])[:15]
                    row_data.append(f"[{j}]{cell_value}")
                print(f"행 {i}: {' | '.join(row_data)}")
            
            # 키워드 검색
            keywords = ['4차', '5차', '상품단가구간', '수리_부품교체', '수리_세척', '수리_SW', '수리_기타', '검수']
            print(f"\n키워드 검색:")
            
            for keyword in keywords:
                for i in range(min(10, len(df))):
                    for j in range(len(df.columns)):
                        cell_value = str(df.iloc[i, j])
                        if keyword == cell_value:
                            print(f"  '{keyword}' 발견: 행 {i+1}, 열 {j+1}")
        
        wb.close()
        
    except Exception as e:
        print(f"price_new.xlsx 분석 오류: {e}")

def test_header_loading():
    """헤더 로딩 테스트"""
    print("\n=== 헤더 로딩 테스트 ===")
    
    try:
        # 4.xlsx 첫 번째 헤더 테스트
        print("4.xlsx 헤더 테스트:")
        for header_row in range(3):
            try:
                df4 = pd.read_excel("4.xlsx", header=header_row)
                print(f"  헤더 행 {header_row}: {list(df4.columns)[:10]}")
                if any(col in ['QAID', '4차', '5차', '상품단가구간', '수리결과'] for col in df4.columns):
                    print(f"    -> 유효한 헤더 발견!")
                    print(f"    -> 데이터 샘플:")
                    print(df4.head(3))
                    break
            except:
                continue
        
        # price_new.xlsx 두 번째 헤더 테스트
        print("\nprice_new.xlsx 헤더 테스트:")
        for header_row in range(3):
            try:
                df_price = pd.read_excel("price_new.xlsx", sheet_name="데이터유효성_일반", header=header_row)
                print(f"  헤더 행 {header_row}: {list(df_price.columns)[:10]}")
                if any(col in ['4차', '5차', '상품단가구간', '수리_부품교체', '수리_세척'] for col in df_price.columns):
                    print(f"    -> 유효한 헤더 발견!")
                    print(f"    -> 데이터 샘플:")
                    print(df_price.head(3))
                    break
            except:
                continue
                
    except Exception as e:
        print(f"헤더 테스트 오류: {e}")

if __name__ == "__main__":
    analyze_4xlsx()
    analyze_price_new()
    test_header_loading()
