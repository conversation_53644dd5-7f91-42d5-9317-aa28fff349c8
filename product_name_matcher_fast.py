import pandas as pd
import numpy as np
import re
from collections import defaultdict

def load_4xlsx_product_data():
    """4.xlsx 파일에서 제품명 관련 데이터를 로드합니다."""
    print("=== 4.xlsx 파일 로드 ===")
    
    # 첫 번째 헤더 (행 1)로 로드
    df = pd.read_excel("4.xlsx", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['QAID', '제품명', '상품단가구간', '수리결과']
    df_selected = df[required_columns].copy()
    
    # NaN 값 제거
    df_selected = df_selected.dropna().reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    print(f"제품명 고유값: {df_selected['제품명'].nunique()}개")
    
    return df_selected

def load_price_monitor_data():
    """price_new.xlsx의 모니터사이즈(미기재품목) 시트를 로드합니다."""
    print("\n=== price_new.xlsx 모니터사이즈(미기재품목) 시트 로드 ===")
    
    # 헤더 행 1로 로드
    df = pd.read_excel("price_new.xlsx", sheet_name="모니터사이즈(미기재품목)", header=1)
    
    # 필요한 컬럼만 선택
    required_columns = ['제품명', '수리_부품교체', '수리_세척', '수리_기타', '검수']
    df_selected = df[required_columns].copy()
    
    # NaN 값이 있는 행 제거
    df_selected = df_selected.dropna(subset=['제품명']).reset_index(drop=True)
    
    print(f"로드된 데이터: {len(df_selected)}개")
    
    return df_selected

def extract_keywords(product_name):
    """제품명에서 주요 키워드를 추출합니다."""
    if pd.isna(product_name):
        return set()
    
    # 문자열로 변환
    name = str(product_name).lower()
    
    # 특수 문자 제거 및 단어 분리
    name = re.sub(r'[^\w\s가-힣]', ' ', name)
    words = name.split()
    
    # 의미있는 키워드만 추출 (길이 2 이상)
    keywords = set()
    for word in words:
        if len(word) >= 2:
            keywords.add(word)
    
    return keywords

def create_keyword_index(df_price):
    """price_new.xlsx의 제품명에서 키워드 인덱스를 생성합니다."""
    print("키워드 인덱스 생성 중...")
    
    keyword_to_products = defaultdict(set)
    
    for idx, row in df_price.iterrows():
        제품명 = row['제품명']
        keywords = extract_keywords(제품명)
        
        for keyword in keywords:
            keyword_to_products[keyword].add(idx)
    
    print(f"키워드 인덱스 생성 완료: {len(keyword_to_products)}개 키워드")
    return keyword_to_products

def find_matching_products(target_product, keyword_index, df_price, min_common_keywords=2):
    """키워드 기반으로 매칭되는 제품들을 찾습니다."""
    target_keywords = extract_keywords(target_product)
    
    if not target_keywords:
        return []
    
    # 각 제품별 공통 키워드 수 계산
    product_scores = defaultdict(int)
    
    for keyword in target_keywords:
        if keyword in keyword_index:
            for product_idx in keyword_index[keyword]:
                product_scores[product_idx] += 1
    
    # 최소 공통 키워드 수 이상인 제품들만 선택
    matching_products = []
    for product_idx, score in product_scores.items():
        if score >= min_common_keywords:
            matching_products.append((product_idx, score))
    
    # 점수 순으로 정렬
    matching_products.sort(key=lambda x: x[1], reverse=True)
    
    return matching_products

def perform_fast_matching(df_4xlsx, df_price):
    """빠른 키워드 기반 매칭을 수행합니다."""
    print("\n=== 빠른 키워드 기반 매칭 수행 ===")
    
    # 키워드 인덱스 생성
    keyword_index = create_keyword_index(df_price)
    
    # price_new.xlsx에서 제품명과 가격 정보 딕셔너리 생성
    price_dict = {}
    for idx, row in df_price.iterrows():
        price_dict[idx] = {
            '제품명': row['제품명'],
            '수리_부품교체': row['수리_부품교체'],
            '수리_세척': row['수리_세척'],
            '수리_기타': row['수리_기타'],
            '검수': row['검수']
        }
    
    # 결과 데이터 리스트
    result_data = []
    
    # 매칭 통계
    매칭_성공 = 0
    매칭_실패 = 0
    유효성_통과 = 0
    유효성_실패 = 0
    
    print("매칭 진행 중...")
    
    # 4.xlsx의 각 항목에 대해 매칭 수행
    for idx, row in df_4xlsx.iterrows():
        qaid = row['QAID']
        제품명 = row['제품명']
        상품단가구간 = row['상품단가구간']
        수리결과 = row['수리결과']
        
        # 키워드 기반 매칭
        matching_products = find_matching_products(제품명, keyword_index, df_price)
        
        if matching_products:
            # 가장 높은 점수의 제품 선택
            best_match_idx, score = matching_products[0]
            매칭_성공 += 1
            
            price_info = price_dict[best_match_idx]
            matched_product_name = price_info['제품명']
            
            # 수리결과에 해당하는 가격 정보 확인
            if 수리결과 in price_info:
                예상가격 = price_info[수리결과]
                유효성_통과 += 1
                유효성검증 = "통과"
            else:
                예상가격 = "해당없음"
                유효성_실패 += 1
                유효성검증 = "실패 - 수리결과 불일치"
            
            result_data.append({
                'QAID': qaid,
                '제품명': 제품명,
                '매칭된_제품명': matched_product_name,
                '키워드_점수': score,
                '상품단가구간': 상품단가구간,
                '수리결과': 수리결과,
                '매칭상태': "매칭됨",
                '예상가격': 예상가격,
                '수리_부품교체': price_info['수리_부품교체'],
                '수리_세척': price_info['수리_세척'],
                '수리_기타': price_info['수리_기타'],
                '검수': price_info['검수'],
                '유효성검증': 유효성검증
            })
        else:
            매칭_실패 += 1
            유효성_실패 += 1
            
            result_data.append({
                'QAID': qaid,
                '제품명': 제품명,
                '매칭된_제품명': "매칭실패",
                '키워드_점수': 0,
                '상품단가구간': 상품단가구간,
                '수리결과': 수리결과,
                '매칭상태': "별도항목 - 매칭실패",
                '예상가격': "매칭실패",
                '수리_부품교체': "매칭실패",
                '수리_세척': "매칭실패",
                '수리_기타': "매칭실패",
                '검수': "매칭실패",
                '유효성검증': "실패 - 매칭불가"
            })
        
        # 진행상황 출력
        if (idx + 1) % 5000 == 0:
            print(f"진행률: {idx + 1}/{len(df_4xlsx)} ({(idx + 1)/len(df_4xlsx)*100:.1f}%)")
    
    # DataFrame으로 변환
    result_df = pd.DataFrame(result_data)
    
    # 통계 출력
    print(f"\n=== 매칭 및 검증 통계 ===")
    print(f"총 처리 항목: {len(result_df)}")
    print(f"매칭 성공: {매칭_성공}개 ({매칭_성공/len(result_df)*100:.1f}%)")
    print(f"매칭 실패: {매칭_실패}개 ({매칭_실패/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 통과: {유효성_통과}개 ({유효성_통과/len(result_df)*100:.1f}%)")
    print(f"유효성 검증 실패: {유효성_실패}개 ({유효성_실패/len(result_df)*100:.1f}%)")
    
    return result_df

def analyze_results(result_df):
    """결과 분석을 수행합니다."""
    print(f"\n=== 결과 분석 ===")
    
    # 매칭 상태별 통계
    print("매칭 상태별 통계:")
    매칭상태_통계 = result_df['매칭상태'].value_counts()
    for 상태, 개수 in 매칭상태_통계.items():
        print(f"  {상태}: {개수}개")
    
    # 수리결과별 매칭 성공률
    print("\n수리결과별 매칭 성공률:")
    수리결과별_통계 = result_df.groupby('수리결과').agg({
        '매칭상태': lambda x: (x == '매칭됨').sum(),
        'QAID': 'count'
    }).rename(columns={'매칭상태': '매칭성공', 'QAID': '총개수'})
    수리결과별_통계['성공률'] = (수리결과별_통계['매칭성공'] / 수리결과별_통계['총개수'] * 100).round(1)
    print(수리결과별_통계)
    
    # 키워드 점수 분포
    매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
    if not 매칭성공.empty:
        print(f"\n키워드 점수 분포 (매칭 성공 항목):")
        print(f"  평균 점수: {매칭성공['키워드_점수'].mean():.1f}")
        print(f"  최고 점수: {매칭성공['키워드_점수'].max()}")
        print(f"  최저 점수: {매칭성공['키워드_점수'].min()}")

def main():
    """메인 실행 함수"""
    print("4.xlsx와 price_new.xlsx 제품명 매칭 및 유효성 검증 프로그램 (고속 버전)")
    print("=" * 80)
    
    # 파일 로드
    df_4xlsx = load_4xlsx_product_data()
    df_price = load_price_monitor_data()
    
    # 매칭 및 유효성 검증 수행
    result_df = perform_fast_matching(df_4xlsx, df_price)
    
    # 결과 분석
    analyze_results(result_df)
    
    # 결과 미리보기
    print(f"\n=== 결과 미리보기 (처음 10개) ===")
    display_columns = ['QAID', '제품명', '매칭된_제품명', '키워드_점수', '수리결과', '예상가격', '매칭상태']
    print(result_df[display_columns].head(10))
    
    # 매칭 성공 사례 미리보기
    매칭성공 = result_df[result_df['매칭상태'] == '매칭됨']
    if not 매칭성공.empty:
        print(f"\n=== 매칭 성공 사례 (처음 5개) ===")
        print(매칭성공[display_columns].head())
    
    # 결과 파일 저장
    output_filename = "제품명_매칭_유효성검증_결과.xlsx"
    
    # 여러 시트로 저장
    with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
        # 전체 결과
        result_df.to_excel(writer, sheet_name='전체결과', index=False)
        
        # 매칭 성공 항목만
        if not 매칭성공.empty:
            매칭성공.to_excel(writer, sheet_name='매칭성공', index=False)
        
        # 별도항목 (매칭 실패)
        별도항목 = result_df[result_df['매칭상태'].str.contains('별도항목')]
        if not 별도항목.empty:
            별도항목.to_excel(writer, sheet_name='별도항목_매칭실패', index=False)
        
        # 유효성 검증 실패 항목
        유효성실패 = result_df[result_df['유효성검증'].str.contains('실패')]
        if not 유효성실패.empty:
            유효성실패.to_excel(writer, sheet_name='유효성검증실패', index=False)
    
    print(f"\n결과 파일이 '{output_filename}'로 저장되었습니다.")
    print("시트 구성: 전체결과, 매칭성공, 별도항목_매칭실패, 유효성검증실패")

if __name__ == "__main__":
    main()
