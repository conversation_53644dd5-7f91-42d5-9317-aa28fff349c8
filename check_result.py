import pandas as pd

# 결과 파일 확인
df_result = pd.read_excel("매칭_결과.xlsx")

print("=== 매칭 결과 파일 내용 ===")
print(df_result)

print(f"\n=== 요약 정보 ===")
print(f"총 항목 수: {len(df_result)}")
print(f"매칭 성공 항목: {len(df_result[df_result['매칭된_카테고리'].notna()])}")

print(f"\n=== 카테고리별 집계 ===")
category_summary = df_result.groupby('매칭된_카테고리').agg({
    '수량': 'first',
    '수리비용': 'first', 
    '별도수리비': 'first',
    '추가비용': 'first',
    '처리내용': 'count'
}).rename(columns={'처리내용': '항목수'})

print(category_summary)
