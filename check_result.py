import pandas as pd

# 결과 파일 확인
df_result = pd.read_excel("매칭_결과.xlsx")

print("=== 매칭 결과 파일 내용 ===")
print(df_result)

print(f"\n=== 요약 정보 ===")
print(f"총 항목 수: {len(df_result)}")
print(f"매칭 성공 항목: {len(df_result[df_result['매칭상태'] == '매칭됨'])}")
print(f"매칭 안됨: {len(df_result[df_result['매칭상태'] == '매칭 안됨'])}")
print(f"1.xlsx 전용: {len(df_result[df_result['매칭상태'] == '1.xlsx 전용'])}")

print(f"\n=== 2.xlsx 카테고리별 집계 ===")
category_summary = df_result.groupby('2xlsx_카테고리').agg({
    '2xlsx_수량': 'first',
    '2xlsx_수리비용': 'first',
    '2xlsx_별도수리비': 'first',
    '2xlsx_추가비용': 'first',
    '1xlsx_처리내용': 'count'
}).rename(columns={'1xlsx_처리내용': '매칭된_1xlsx_항목수'})

print(category_summary)

print(f"\n=== 매칭 상태별 집계 ===")
status_summary = df_result['매칭상태'].value_counts()
print(status_summary)
