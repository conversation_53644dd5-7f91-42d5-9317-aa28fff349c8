import pandas as pd
import openpyxl

def analyze_4xlsx_product_name():
    """4.xlsx 파일에서 제품명 컬럼을 찾아 분석합니다."""
    print("=== 4.xlsx 파일 제품명 분석 ===")
    
    try:
        # 첫 번째 헤더로 로드
        df = pd.read_excel("4.xlsx", header=1)
        print(f"컬럼명: {list(df.columns)}")
        
        # 제품명 컬럼이 있는지 확인
        if '제품명' in df.columns:
            print("제품명 컬럼 발견!")
            required_columns = ['QAID', '제품명', '상품단가구간', '수리결과']
            df_selected = df[required_columns].copy()
            df_selected = df_selected.dropna().reset_index(drop=True)
            
            print(f"로드된 데이터: {len(df_selected)}개")
            print("데이터 샘플:")
            print(df_selected.head(10))
            
            print(f"\n제품명 고유값 개수: {df_selected['제품명'].nunique()}개")
            print("제품명 샘플 (처음 20개):")
            unique_products = df_selected['제품명'].unique()[:20]
            for i, product in enumerate(unique_products, 1):
                print(f"  {i}. {product}")
                
            return df_selected
        else:
            print("제품명 컬럼을 찾을 수 없습니다.")
            print("사용 가능한 컬럼들을 확인해보겠습니다.")
            
            # 모든 컬럼 확인
            for col in df.columns:
                if '제품' in str(col) or '상품' in str(col) or '품목' in str(col):
                    print(f"관련 컬럼 발견: {col}")
                    print(f"  샘플 데이터: {df[col].dropna().head(5).tolist()}")
            
            return None
            
    except Exception as e:
        print(f"4.xlsx 분석 오류: {e}")
        return None

def analyze_price_monitor_sheet():
    """price_new.xlsx의 모니터사이즈(미기재품목) 시트를 분석합니다."""
    print("\n=== price_new.xlsx 모니터사이즈(미기재품목) 시트 분석 ===")
    
    try:
        # 시트 목록 확인
        wb = openpyxl.load_workbook("price_new.xlsx")
        print(f"사용 가능한 시트: {wb.sheetnames}")
        
        if "모니터사이즈(미기재품목)" in wb.sheetnames:
            print("모니터사이즈(미기재품목) 시트 발견!")
            
            # 시트 구조 확인
            df = pd.read_excel("price_new.xlsx", sheet_name="모니터사이즈(미기재품목)", header=None)
            print(f"시트 크기: {df.shape}")
            
            # 첫 10행 확인하여 헤더 위치 찾기
            print("첫 10행 데이터:")
            for i in range(min(10, len(df))):
                row_data = []
                for j in range(min(10, len(df.columns))):
                    cell_value = str(df.iloc[i, j])[:20]
                    row_data.append(f"[{j}]{cell_value}")
                print(f"행 {i}: {' | '.join(row_data)}")
            
            # 제품명, 수리 관련 키워드 검색
            keywords = ['제품명', '수리_부품교체', '수리_세척', '수리_기타', '검수']
            print(f"\n키워드 검색:")
            
            for keyword in keywords:
                for i in range(min(10, len(df))):
                    for j in range(len(df.columns)):
                        cell_value = str(df.iloc[i, j])
                        if keyword == cell_value:
                            print(f"  '{keyword}' 발견: 행 {i+1}, 열 {j+1}")
            
            # 헤더 행 테스트
            print(f"\n헤더 행 테스트:")
            for header_row in range(3):
                try:
                    df_test = pd.read_excel("price_new.xlsx", sheet_name="모니터사이즈(미기재품목)", header=header_row)
                    print(f"  헤더 행 {header_row}: {list(df_test.columns)[:10]}")
                    if any(col in ['제품명', '수리_부품교체', '수리_세척'] for col in df_test.columns):
                        print(f"    -> 유효한 헤더 발견!")
                        print(f"    -> 데이터 샘플:")
                        print(df_test.head(3))
                        return df_test
                except:
                    continue
        else:
            print("모니터사이즈(미기재품목) 시트를 찾을 수 없습니다.")
            
        wb.close()
        return None
        
    except Exception as e:
        print(f"price_new.xlsx 분석 오류: {e}")
        return None

if __name__ == "__main__":
    df_4xlsx = analyze_4xlsx_product_name()
    df_price = analyze_price_monitor_sheet()
